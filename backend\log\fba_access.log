2025-09-10 00:04:19.405 | INFO     | f7d826e6a5614c46a668c3a72a2f9210 | 成功认证Java用户: admin
2025-09-10 00:04:20.070 | INFO     | f7d826e6a5614c46a668c3a72a2f9210 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:04:20.072 | INFO     | f7d826e6a5614c46a668c3a72a2f9210 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 670.461ms
2025-09-10 00:09:19.420 | INFO     | bf102a402ec245c8a738259106004d13 | 成功认证Java用户: admin
2025-09-10 00:09:20.087 | INFO     | bf102a402ec245c8a738259106004d13 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:09:20.088 | INFO     | bf102a402ec245c8a738259106004d13 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 669.902ms
2025-09-10 00:14:19.404 | INFO     | 112999cebb7e42daad442ee37dd0e583 | 成功认证Java用户: admin
2025-09-10 00:14:20.062 | INFO     | 112999cebb7e42daad442ee37dd0e583 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:14:20.065 | INFO     | 112999cebb7e42daad442ee37dd0e583 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 664.379ms
2025-09-10 00:19:19.416 | INFO     | b1f4e33ea6ce457abb32c88b7e3c0263 | 成功认证Java用户: admin
2025-09-10 00:19:20.095 | INFO     | b1f4e33ea6ce457abb32c88b7e3c0263 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:19:20.098 | INFO     | b1f4e33ea6ce457abb32c88b7e3c0263 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 684.719ms
2025-09-10 00:24:19.414 | INFO     | f50b2c54c4a44cd48208837fe57a3818 | 成功认证Java用户: admin
2025-09-10 00:24:20.061 | INFO     | f50b2c54c4a44cd48208837fe57a3818 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:24:20.063 | INFO     | f50b2c54c4a44cd48208837fe57a3818 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 650.046ms
2025-09-10 00:29:19.408 | INFO     | 74ca5d071ed44fa19b634c2faec47c2d | 成功认证Java用户: admin
2025-09-10 00:29:20.076 | INFO     | 74ca5d071ed44fa19b634c2faec47c2d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:29:20.079 | INFO     | 74ca5d071ed44fa19b634c2faec47c2d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 674.029ms
2025-09-10 00:34:19.403 | INFO     | 510cecdf68db4df4ac739ccfeb0ee2f5 | 成功认证Java用户: admin
2025-09-10 00:34:20.058 | INFO     | 510cecdf68db4df4ac739ccfeb0ee2f5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:34:20.060 | INFO     | 510cecdf68db4df4ac739ccfeb0ee2f5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 658.231ms
2025-09-10 00:39:19.411 | INFO     | 393ba16a94854bfb92a273112427c645 | 成功认证Java用户: admin
2025-09-10 00:39:20.078 | INFO     | 393ba16a94854bfb92a273112427c645 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:39:20.080 | INFO     | 393ba16a94854bfb92a273112427c645 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 672.020ms
2025-09-10 00:44:19.409 | INFO     | fb3810e970f7491ab77522e2ffec4b3f | 成功认证Java用户: admin
2025-09-10 00:44:20.062 | INFO     | fb3810e970f7491ab77522e2ffec4b3f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:44:20.064 | INFO     | fb3810e970f7491ab77522e2ffec4b3f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 657.087ms
2025-09-10 00:49:19.408 | INFO     | 5a8e70acfcd6446285d004c47c2dc20f | 成功认证Java用户: admin
2025-09-10 00:49:20.055 | INFO     | 5a8e70acfcd6446285d004c47c2dc20f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:49:20.057 | INFO     | 5a8e70acfcd6446285d004c47c2dc20f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 650.488ms
2025-09-10 00:54:19.424 | INFO     | 73e2e6f2d3d64fddb9b0d61232ed9e5b | 成功认证Java用户: admin
2025-09-10 00:54:20.077 | INFO     | 73e2e6f2d3d64fddb9b0d61232ed9e5b | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:54:20.080 | INFO     | 73e2e6f2d3d64fddb9b0d61232ed9e5b | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 657.483ms
2025-09-10 00:59:19.411 | INFO     | 560b9e7153b34df09dcc44721e3f6727 | 成功认证Java用户: admin
2025-09-10 00:59:20.089 | INFO     | 560b9e7153b34df09dcc44721e3f6727 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 00:59:20.091 | INFO     | 560b9e7153b34df09dcc44721e3f6727 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 682.553ms
2025-09-10 01:04:19.407 | INFO     | 01b7ae2fbb384c8c8f2de1fb27404535 | 成功认证Java用户: admin
2025-09-10 01:04:20.074 | INFO     | 01b7ae2fbb384c8c8f2de1fb27404535 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:04:20.076 | INFO     | 01b7ae2fbb384c8c8f2de1fb27404535 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 670.558ms
2025-09-10 01:09:19.411 | INFO     | 543bb6c99d5d4873a956b630d50e25e6 | 成功认证Java用户: admin
2025-09-10 01:09:20.088 | INFO     | 543bb6c99d5d4873a956b630d50e25e6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:09:20.089 | INFO     | 543bb6c99d5d4873a956b630d50e25e6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 680.465ms
2025-09-10 01:14:19.410 | INFO     | b6859f12e5694df6a7b2944a1e0d515c | 成功认证Java用户: admin
2025-09-10 01:14:20.060 | INFO     | b6859f12e5694df6a7b2944a1e0d515c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:14:20.062 | INFO     | b6859f12e5694df6a7b2944a1e0d515c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 654.941ms
2025-09-10 01:19:19.416 | INFO     | d54d795254b546ad8f93ac71636193e9 | 成功认证Java用户: admin
2025-09-10 01:19:20.100 | INFO     | d54d795254b546ad8f93ac71636193e9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:19:20.103 | INFO     | d54d795254b546ad8f93ac71636193e9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 689.548ms
2025-09-10 01:24:19.403 | INFO     | fd732b3ba2244f48bcd823a94c64e599 | 成功认证Java用户: admin
2025-09-10 01:24:20.057 | INFO     | fd732b3ba2244f48bcd823a94c64e599 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:24:20.058 | INFO     | fd732b3ba2244f48bcd823a94c64e599 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 657.060ms
2025-09-10 01:29:19.410 | INFO     | e939cbb172954ffb86a79e2688e2dcfc | 成功认证Java用户: admin
2025-09-10 01:29:20.088 | INFO     | e939cbb172954ffb86a79e2688e2dcfc | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:29:20.091 | INFO     | e939cbb172954ffb86a79e2688e2dcfc | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 683.063ms
2025-09-10 01:34:19.421 | INFO     | 880658f1afdb471c9f4634955eef66ef | 成功认证Java用户: admin
2025-09-10 01:34:20.076 | INFO     | 880658f1afdb471c9f4634955eef66ef | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:34:20.078 | INFO     | 880658f1afdb471c9f4634955eef66ef | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 658.854ms
2025-09-10 01:39:19.407 | INFO     | eb9c239d73a9410d84702a618ab868d6 | 成功认证Java用户: admin
2025-09-10 01:39:20.098 | INFO     | eb9c239d73a9410d84702a618ab868d6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:39:20.100 | INFO     | eb9c239d73a9410d84702a618ab868d6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 695.622ms
2025-09-10 01:44:19.410 | INFO     | 28487f6045f749519a3e5e897887b1d7 | 成功认证Java用户: admin
2025-09-10 01:44:20.099 | INFO     | 28487f6045f749519a3e5e897887b1d7 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:44:20.100 | INFO     | 28487f6045f749519a3e5e897887b1d7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 692.993ms
2025-09-10 01:49:19.414 | INFO     | d2e48417a166409fbfe7067ed1fb0d98 | 成功认证Java用户: admin
2025-09-10 01:49:20.076 | INFO     | d2e48417a166409fbfe7067ed1fb0d98 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:49:20.079 | INFO     | d2e48417a166409fbfe7067ed1fb0d98 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 666.479ms
2025-09-10 01:54:19.405 | INFO     | 95c44599552246bbb771502c19974ced | 成功认证Java用户: admin
2025-09-10 01:54:20.059 | INFO     | 95c44599552246bbb771502c19974ced | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:54:20.062 | INFO     | 95c44599552246bbb771502c19974ced | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 659.167ms
2025-09-10 01:59:19.402 | INFO     | 660fd44b3760482db568698c09995a7c | 成功认证Java用户: admin
2025-09-10 01:59:20.056 | INFO     | 660fd44b3760482db568698c09995a7c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 01:59:20.059 | INFO     | 660fd44b3760482db568698c09995a7c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 659.163ms
2025-09-10 02:04:19.416 | INFO     | 113b0e1cd36747f790c51459631db0cc | 成功认证Java用户: admin
2025-09-10 02:04:20.048 | INFO     | 113b0e1cd36747f790c51459631db0cc | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:04:20.052 | INFO     | 113b0e1cd36747f790c51459631db0cc | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 638.663ms
2025-09-10 02:09:19.414 | INFO     | ccf98cb3d714479cb3a4357644e5b7f7 | 成功认证Java用户: admin
2025-09-10 02:09:20.085 | INFO     | ccf98cb3d714479cb3a4357644e5b7f7 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:09:20.086 | INFO     | ccf98cb3d714479cb3a4357644e5b7f7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 674.987ms
2025-09-10 02:14:19.424 | INFO     | 80cad69d081342dc8902d5a245583b42 | 成功认证Java用户: admin
2025-09-10 02:14:20.082 | INFO     | 80cad69d081342dc8902d5a245583b42 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:14:20.084 | INFO     | 80cad69d081342dc8902d5a245583b42 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 662.854ms
2025-09-10 02:19:19.414 | INFO     | 84b48c52df2f46a2ab8f5943535f033f | 成功认证Java用户: admin
2025-09-10 02:19:20.059 | INFO     | 84b48c52df2f46a2ab8f5943535f033f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:19:20.063 | INFO     | 84b48c52df2f46a2ab8f5943535f033f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 651.601ms
2025-09-10 02:24:19.403 | INFO     | d69bf6979dc14eb1868bb7e238838cc4 | 成功认证Java用户: admin
2025-09-10 02:24:20.073 | INFO     | d69bf6979dc14eb1868bb7e238838cc4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:24:20.075 | INFO     | d69bf6979dc14eb1868bb7e238838cc4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 675.190ms
2025-09-10 02:29:19.411 | INFO     | b0a036ddc5a54959880409806daf725d | 成功认证Java用户: admin
2025-09-10 02:29:20.089 | INFO     | b0a036ddc5a54959880409806daf725d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:29:20.092 | INFO     | b0a036ddc5a54959880409806daf725d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 683.288ms
2025-09-10 02:34:19.413 | INFO     | 5914044d0354421c941f5b29e5b39417 | 成功认证Java用户: admin
2025-09-10 02:34:20.074 | INFO     | 5914044d0354421c941f5b29e5b39417 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:34:20.076 | INFO     | 5914044d0354421c941f5b29e5b39417 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 665.989ms
2025-09-10 02:39:19.411 | INFO     | 5a4cb6ead865404e9f647fb8be27386b | 成功认证Java用户: admin
2025-09-10 02:39:20.083 | INFO     | 5a4cb6ead865404e9f647fb8be27386b | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:39:20.085 | INFO     | 5a4cb6ead865404e9f647fb8be27386b | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 676.076ms
2025-09-10 02:44:19.411 | INFO     | 62893fe0be3d4958a192cb9ed8b3f6c0 | 成功认证Java用户: admin
2025-09-10 02:44:20.098 | INFO     | 62893fe0be3d4958a192cb9ed8b3f6c0 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:44:20.101 | INFO     | 62893fe0be3d4958a192cb9ed8b3f6c0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 692.266ms
2025-09-10 02:49:19.401 | INFO     | 563846eb6fe945a48c77208a52982f52 | 成功认证Java用户: admin
2025-09-10 02:49:20.047 | INFO     | 563846eb6fe945a48c77208a52982f52 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:49:20.049 | INFO     | 563846eb6fe945a48c77208a52982f52 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 649.463ms
2025-09-10 02:54:19.409 | INFO     | e9bccf187f7543a0ae3fe6a09e46227e | 成功认证Java用户: admin
2025-09-10 02:54:20.065 | INFO     | e9bccf187f7543a0ae3fe6a09e46227e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:54:20.068 | INFO     | e9bccf187f7543a0ae3fe6a09e46227e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 660.189ms
2025-09-10 02:59:19.405 | INFO     | ae144b6020874f1fa7f7be73e3c10fdc | 成功认证Java用户: admin
2025-09-10 02:59:20.049 | INFO     | ae144b6020874f1fa7f7be73e3c10fdc | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 02:59:20.051 | INFO     | ae144b6020874f1fa7f7be73e3c10fdc | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 649.734ms
2025-09-10 03:04:19.420 | INFO     | 32f7cf9c3cd54d60a583a3ef7a3cc854 | 成功认证Java用户: admin
2025-09-10 03:04:20.069 | INFO     | 32f7cf9c3cd54d60a583a3ef7a3cc854 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:04:20.072 | INFO     | 32f7cf9c3cd54d60a583a3ef7a3cc854 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 654.964ms
2025-09-10 03:09:19.412 | INFO     | ea9a3bcfe9d34936b30ee162f06e780f | 成功认证Java用户: admin
2025-09-10 03:09:20.073 | INFO     | ea9a3bcfe9d34936b30ee162f06e780f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:09:20.074 | INFO     | ea9a3bcfe9d34936b30ee162f06e780f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 665.188ms
2025-09-10 03:14:19.403 | INFO     | b6e1a32171ae40a6af95f6b70db4ec89 | 成功认证Java用户: admin
2025-09-10 03:14:20.045 | INFO     | b6e1a32171ae40a6af95f6b70db4ec89 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:14:20.047 | INFO     | b6e1a32171ae40a6af95f6b70db4ec89 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 646.983ms
2025-09-10 03:19:19.413 | INFO     | c5cb85bf7f444622ae06be452295738f | 成功认证Java用户: admin
2025-09-10 03:19:20.089 | INFO     | c5cb85bf7f444622ae06be452295738f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:19:20.092 | INFO     | c5cb85bf7f444622ae06be452295738f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 680.510ms
2025-09-10 03:24:19.411 | INFO     | 52691b2a05b649dcaf87fb7b5348ab44 | 成功认证Java用户: admin
2025-09-10 03:24:20.067 | INFO     | 52691b2a05b649dcaf87fb7b5348ab44 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:24:20.069 | INFO     | 52691b2a05b649dcaf87fb7b5348ab44 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 660.694ms
2025-09-10 03:29:19.411 | INFO     | e2e9ad401eae4b68a05be22e3c0834af | 成功认证Java用户: admin
2025-09-10 03:29:20.063 | INFO     | e2e9ad401eae4b68a05be22e3c0834af | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:29:20.064 | INFO     | e2e9ad401eae4b68a05be22e3c0834af | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 655.521ms
2025-09-10 03:34:19.408 | INFO     | 5ad98b1c5d3949d29014105c8ccdb672 | 成功认证Java用户: admin
2025-09-10 03:34:20.095 | INFO     | 5ad98b1c5d3949d29014105c8ccdb672 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:34:20.098 | INFO     | 5ad98b1c5d3949d29014105c8ccdb672 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 692.569ms
2025-09-10 03:39:19.402 | INFO     | e25deb2467ef41649616f9d6db4918a9 | 成功认证Java用户: admin
2025-09-10 03:39:20.075 | INFO     | e25deb2467ef41649616f9d6db4918a9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:39:20.076 | INFO     | e25deb2467ef41649616f9d6db4918a9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 676.234ms
2025-09-10 03:44:19.413 | INFO     | c5f4df5db7ea4a75bf64b1a5e0edded6 | 成功认证Java用户: admin
2025-09-10 03:44:20.068 | INFO     | c5f4df5db7ea4a75bf64b1a5e0edded6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:44:20.069 | INFO     | c5f4df5db7ea4a75bf64b1a5e0edded6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 658.233ms
2025-09-10 03:49:19.401 | INFO     | 31512da7f4384cc6a504264f0ca1fb00 | 成功认证Java用户: admin
2025-09-10 03:49:20.052 | INFO     | 31512da7f4384cc6a504264f0ca1fb00 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:49:20.053 | INFO     | 31512da7f4384cc6a504264f0ca1fb00 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 654.019ms
2025-09-10 03:54:19.408 | INFO     | 18a6840d168f4768b4e3eb7ebe0632e2 | 成功认证Java用户: admin
2025-09-10 03:54:20.113 | INFO     | 18a6840d168f4768b4e3eb7ebe0632e2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:54:20.115 | INFO     | 18a6840d168f4768b4e3eb7ebe0632e2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 709.353ms
2025-09-10 03:59:19.413 | INFO     | 1be8f72555654464a39140abab8ee1c1 | 成功认证Java用户: admin
2025-09-10 03:59:20.057 | INFO     | 1be8f72555654464a39140abab8ee1c1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 03:59:20.059 | INFO     | 1be8f72555654464a39140abab8ee1c1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 648.736ms
2025-09-10 04:04:19.423 | INFO     | 46c59912a4be49ecac8b0b526f23dd79 | 成功认证Java用户: admin
2025-09-10 04:04:20.085 | INFO     | 46c59912a4be49ecac8b0b526f23dd79 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:04:20.086 | INFO     | 46c59912a4be49ecac8b0b526f23dd79 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 666.403ms
2025-09-10 04:09:19.411 | INFO     | f8a0c182e69c4d48b0b38d129c93aecf | 成功认证Java用户: admin
2025-09-10 04:09:20.095 | INFO     | f8a0c182e69c4d48b0b38d129c93aecf | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:09:20.098 | INFO     | f8a0c182e69c4d48b0b38d129c93aecf | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 689.356ms
2025-09-10 04:14:19.415 | INFO     | f6cf008ee2544b5088507b8502183ed2 | 成功认证Java用户: admin
2025-09-10 04:14:20.059 | INFO     | f6cf008ee2544b5088507b8502183ed2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:14:20.063 | INFO     | f6cf008ee2544b5088507b8502183ed2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 650.485ms
2025-09-10 04:19:19.412 | INFO     | c3603dcc4e4a46f29096319956142bd3 | 成功认证Java用户: admin
2025-09-10 04:19:20.057 | INFO     | c3603dcc4e4a46f29096319956142bd3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:19:20.060 | INFO     | c3603dcc4e4a46f29096319956142bd3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 649.620ms
2025-09-10 04:24:19.409 | INFO     | 1ddc74a38d5e4585802afbd2de0ab975 | 成功认证Java用户: admin
2025-09-10 04:24:20.051 | INFO     | 1ddc74a38d5e4585802afbd2de0ab975 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:24:20.054 | INFO     | 1ddc74a38d5e4585802afbd2de0ab975 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 647.353ms
2025-09-10 04:29:19.425 | INFO     | bf92402fe3744941b7a116fe39890158 | 成功认证Java用户: admin
2025-09-10 04:29:20.088 | INFO     | bf92402fe3744941b7a116fe39890158 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:29:20.091 | INFO     | bf92402fe3744941b7a116fe39890158 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 668.599ms
2025-09-10 04:34:19.412 | INFO     | 6155643326e94781857bf33790369cef | 成功认证Java用户: admin
2025-09-10 04:34:20.053 | INFO     | 6155643326e94781857bf33790369cef | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:34:20.056 | INFO     | 6155643326e94781857bf33790369cef | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 645.573ms
2025-09-10 04:39:19.404 | INFO     | 6610a9a132304c23b76eecff80216488 | 成功认证Java用户: admin
2025-09-10 04:39:20.071 | INFO     | 6610a9a132304c23b76eecff80216488 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:39:20.072 | INFO     | 6610a9a132304c23b76eecff80216488 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 671.344ms
2025-09-10 04:44:19.409 | INFO     | e30c2beb4cbb44518fd78e2dc40793df | 成功认证Java用户: admin
2025-09-10 04:44:20.056 | INFO     | e30c2beb4cbb44518fd78e2dc40793df | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:44:20.059 | INFO     | e30c2beb4cbb44518fd78e2dc40793df | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 651.794ms
2025-09-10 04:49:19.403 | INFO     | 3e756c181b05461b8bb709c73ebb1d0b | 成功认证Java用户: admin
2025-09-10 04:49:20.077 | INFO     | 3e756c181b05461b8bb709c73ebb1d0b | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:49:20.078 | INFO     | 3e756c181b05461b8bb709c73ebb1d0b | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 677.174ms
2025-09-10 04:54:19.414 | INFO     | f3a9de915f6f4e7381d3428dbe1f27f5 | 成功认证Java用户: admin
2025-09-10 04:54:20.043 | INFO     | f3a9de915f6f4e7381d3428dbe1f27f5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:54:20.046 | INFO     | f3a9de915f6f4e7381d3428dbe1f27f5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 633.806ms
2025-09-10 04:59:19.405 | INFO     | d2737461f6014c2a9fcc9a75a2e2d036 | 成功认证Java用户: admin
2025-09-10 04:59:20.084 | INFO     | d2737461f6014c2a9fcc9a75a2e2d036 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 04:59:20.085 | INFO     | d2737461f6014c2a9fcc9a75a2e2d036 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 682.536ms
2025-09-10 05:04:19.406 | INFO     | 0825f54683f44e7d83af97cc9abc71b1 | 成功认证Java用户: admin
2025-09-10 05:04:20.077 | INFO     | 0825f54683f44e7d83af97cc9abc71b1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:04:20.078 | INFO     | 0825f54683f44e7d83af97cc9abc71b1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 674.269ms
2025-09-10 05:09:19.416 | INFO     | 2e78e07f11cd4b3eb1b0db9adcc173ae | 成功认证Java用户: admin
2025-09-10 05:09:20.080 | INFO     | 2e78e07f11cd4b3eb1b0db9adcc173ae | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:09:20.081 | INFO     | 2e78e07f11cd4b3eb1b0db9adcc173ae | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 667.096ms
2025-09-10 05:14:19.400 | INFO     | 8aaadde2af914e329c79eae91f9f6d8a | 成功认证Java用户: admin
2025-09-10 05:14:20.063 | INFO     | 8aaadde2af914e329c79eae91f9f6d8a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:14:20.065 | INFO     | 8aaadde2af914e329c79eae91f9f6d8a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 666.590ms
2025-09-10 05:19:19.414 | INFO     | 8941af80fcb640f19b15380d838d36d9 | 成功认证Java用户: admin
2025-09-10 05:19:20.079 | INFO     | 8941af80fcb640f19b15380d838d36d9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:19:20.082 | INFO     | 8941af80fcb640f19b15380d838d36d9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 669.829ms
2025-09-10 05:24:19.416 | INFO     | d880fce6eccc49e98d6e87c9003a66f4 | 成功认证Java用户: admin
2025-09-10 05:24:20.067 | INFO     | d880fce6eccc49e98d6e87c9003a66f4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:24:20.070 | INFO     | d880fce6eccc49e98d6e87c9003a66f4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 655.993ms
2025-09-10 05:29:19.404 | INFO     | 898ff044e7bf4d3b8d537194e376a6fe | 成功认证Java用户: admin
2025-09-10 05:29:20.067 | INFO     | 898ff044e7bf4d3b8d537194e376a6fe | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:29:20.070 | INFO     | 898ff044e7bf4d3b8d537194e376a6fe | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 668.443ms
2025-09-10 05:34:19.407 | INFO     | 75e8971df6f746e0906fb3c5268fb111 | 成功认证Java用户: admin
2025-09-10 05:34:20.070 | INFO     | 75e8971df6f746e0906fb3c5268fb111 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:34:20.073 | INFO     | 75e8971df6f746e0906fb3c5268fb111 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 667.810ms
2025-09-10 05:39:19.419 | INFO     | 6a595cc0b44c406594450137df34a1c4 | 成功认证Java用户: admin
2025-09-10 05:39:20.076 | INFO     | 6a595cc0b44c406594450137df34a1c4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:39:20.079 | INFO     | 6a595cc0b44c406594450137df34a1c4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 661.966ms
2025-09-10 05:44:19.411 | INFO     | 274b59fcae554c4ab055eed854eea84f | 成功认证Java用户: admin
2025-09-10 05:44:20.066 | INFO     | 274b59fcae554c4ab055eed854eea84f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:44:20.068 | INFO     | 274b59fcae554c4ab055eed854eea84f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 659.761ms
2025-09-10 05:49:19.422 | INFO     | f195f670603e4e01b3090354b43d7d32 | 成功认证Java用户: admin
2025-09-10 05:49:20.088 | INFO     | f195f670603e4e01b3090354b43d7d32 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:49:20.089 | INFO     | f195f670603e4e01b3090354b43d7d32 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 669.456ms
2025-09-10 05:54:19.407 | INFO     | ac5fa31e41bf484a80e31cc73ff880bc | 成功认证Java用户: admin
2025-09-10 05:54:20.059 | INFO     | ac5fa31e41bf484a80e31cc73ff880bc | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:54:20.060 | INFO     | ac5fa31e41bf484a80e31cc73ff880bc | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 656.275ms
2025-09-10 05:59:19.404 | INFO     | e980d795bdda41dbb241cb5d12a15c8c | 成功认证Java用户: admin
2025-09-10 05:59:20.060 | INFO     | e980d795bdda41dbb241cb5d12a15c8c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 05:59:20.062 | INFO     | e980d795bdda41dbb241cb5d12a15c8c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 661.527ms
2025-09-10 06:04:19.427 | INFO     | eb9e273ad8934f11a5c0ffb191b53410 | 成功认证Java用户: admin
2025-09-10 06:04:20.067 | INFO     | eb9e273ad8934f11a5c0ffb191b53410 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:04:20.070 | INFO     | eb9e273ad8934f11a5c0ffb191b53410 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 645.323ms
2025-09-10 06:09:19.408 | INFO     | 164994dd2b6347f798e3d166f3cb3889 | 成功认证Java用户: admin
2025-09-10 06:09:20.084 | INFO     | 164994dd2b6347f798e3d166f3cb3889 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:09:20.087 | INFO     | 164994dd2b6347f798e3d166f3cb3889 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 682.787ms
2025-09-10 06:14:19.402 | INFO     | eda7315c426c4ddeb930d01b7c32bcf9 | 成功认证Java用户: admin
2025-09-10 06:14:20.067 | INFO     | eda7315c426c4ddeb930d01b7c32bcf9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:14:20.070 | INFO     | eda7315c426c4ddeb930d01b7c32bcf9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 670.115ms
2025-09-10 06:19:19.416 | INFO     | 74d1402cac824cc085c5ee53b48ae362 | 成功认证Java用户: admin
2025-09-10 06:19:20.095 | INFO     | 74d1402cac824cc085c5ee53b48ae362 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:19:20.097 | INFO     | 74d1402cac824cc085c5ee53b48ae362 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 683.248ms
2025-09-10 06:24:19.404 | INFO     | 45fa40c9b6fd48168ba1ff4d9b01e4e6 | 成功认证Java用户: admin
2025-09-10 06:24:20.090 | INFO     | 45fa40c9b6fd48168ba1ff4d9b01e4e6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:24:20.093 | INFO     | 45fa40c9b6fd48168ba1ff4d9b01e4e6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 691.304ms
2025-09-10 06:29:19.401 | INFO     | 80393a7f17444b878875eff1d789152c | 成功认证Java用户: admin
2025-09-10 06:29:20.080 | INFO     | 80393a7f17444b878875eff1d789152c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:29:20.082 | INFO     | 80393a7f17444b878875eff1d789152c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 682.345ms
2025-09-10 06:34:19.405 | INFO     | 9560659f32f146a1b67e1778ebdc3d38 | 成功认证Java用户: admin
2025-09-10 06:34:20.045 | INFO     | 9560659f32f146a1b67e1778ebdc3d38 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:34:20.047 | INFO     | 9560659f32f146a1b67e1778ebdc3d38 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 643.951ms
2025-09-10 06:39:19.408 | INFO     | bc567bb4eed34a018aca7caf429510a9 | 成功认证Java用户: admin
2025-09-10 06:39:20.078 | INFO     | bc567bb4eed34a018aca7caf429510a9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:39:20.080 | INFO     | bc567bb4eed34a018aca7caf429510a9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 674.070ms
2025-09-10 06:44:19.421 | INFO     | 0b77fc953994407692785a70b97e10ff | 成功认证Java用户: admin
2025-09-10 06:44:20.060 | INFO     | 0b77fc953994407692785a70b97e10ff | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:44:20.063 | INFO     | 0b77fc953994407692785a70b97e10ff | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 644.800ms
2025-09-10 06:49:19.405 | INFO     | 1a00397088c34a7b8980b46f9b2bdcd7 | 成功认证Java用户: admin
2025-09-10 06:49:20.054 | INFO     | 1a00397088c34a7b8980b46f9b2bdcd7 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:49:20.056 | INFO     | 1a00397088c34a7b8980b46f9b2bdcd7 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 653.115ms
2025-09-10 06:54:19.408 | INFO     | a87024ba98f448c2b8843cac7e7ba6ba | 成功认证Java用户: admin
2025-09-10 06:54:20.055 | INFO     | a87024ba98f448c2b8843cac7e7ba6ba | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:54:20.056 | INFO     | a87024ba98f448c2b8843cac7e7ba6ba | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 649.577ms
2025-09-10 06:59:19.410 | INFO     | 5c1c6517995645d5a706d8e00e9e6cad | 成功认证Java用户: admin
2025-09-10 06:59:20.060 | INFO     | 5c1c6517995645d5a706d8e00e9e6cad | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 06:59:20.061 | INFO     | 5c1c6517995645d5a706d8e00e9e6cad | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 653.633ms
2025-09-10 07:04:19.403 | INFO     | 25c2502bcaef4a6695425b043f858a3d | 成功认证Java用户: admin
2025-09-10 07:04:20.054 | INFO     | 25c2502bcaef4a6695425b043f858a3d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:04:20.056 | INFO     | 25c2502bcaef4a6695425b043f858a3d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 655.772ms
2025-09-10 07:09:19.415 | INFO     | a664fec967894c93b668fae97c3bd9fb | 成功认证Java用户: admin
2025-09-10 07:09:20.081 | INFO     | a664fec967894c93b668fae97c3bd9fb | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:09:20.084 | INFO     | a664fec967894c93b668fae97c3bd9fb | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 671.521ms
2025-09-10 07:14:19.405 | INFO     | ab9bc3eb8f204694a360cf14c23d9a39 | 成功认证Java用户: admin
2025-09-10 07:14:20.068 | INFO     | ab9bc3eb8f204694a360cf14c23d9a39 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:14:20.071 | INFO     | ab9bc3eb8f204694a360cf14c23d9a39 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 667.998ms
2025-09-10 07:19:19.409 | INFO     | d0b035c5bcc641509e1c4954aee7d398 | 成功认证Java用户: admin
2025-09-10 07:19:20.074 | INFO     | d0b035c5bcc641509e1c4954aee7d398 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:19:20.076 | INFO     | d0b035c5bcc641509e1c4954aee7d398 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 668.588ms
2025-09-10 07:24:19.425 | INFO     | 5baf82e185994ef299dbdee07f8beeea | 成功认证Java用户: admin
2025-09-10 07:24:20.078 | INFO     | 5baf82e185994ef299dbdee07f8beeea | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:24:20.081 | INFO     | 5baf82e185994ef299dbdee07f8beeea | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 658.794ms
2025-09-10 07:29:19.400 | INFO     | 28e1f5bbb53c4dff8d1eb8a79d446be8 | 成功认证Java用户: admin
2025-09-10 07:29:20.068 | INFO     | 28e1f5bbb53c4dff8d1eb8a79d446be8 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:29:20.071 | INFO     | 28e1f5bbb53c4dff8d1eb8a79d446be8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 673.246ms
2025-09-10 07:34:19.420 | INFO     | 45fe7a61a7514ae5a918d0e84b42e207 | 成功认证Java用户: admin
2025-09-10 07:34:20.077 | INFO     | 45fe7a61a7514ae5a918d0e84b42e207 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:34:20.079 | INFO     | 45fe7a61a7514ae5a918d0e84b42e207 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 661.672ms
2025-09-10 07:39:19.412 | INFO     | cde1176f3af1494caf83ec7c150d4474 | 成功认证Java用户: admin
2025-09-10 07:39:20.076 | INFO     | cde1176f3af1494caf83ec7c150d4474 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:39:20.078 | INFO     | cde1176f3af1494caf83ec7c150d4474 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 667.398ms
2025-09-10 07:44:19.425 | INFO     | 26cce4ad3ea54a5facb097fa71e65c22 | 成功认证Java用户: admin
2025-09-10 07:44:20.064 | INFO     | 26cce4ad3ea54a5facb097fa71e65c22 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:44:20.066 | INFO     | 26cce4ad3ea54a5facb097fa71e65c22 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 643.874ms
2025-09-10 07:49:19.406 | INFO     | d7ce2e2ea74f4bf998d939d92407c767 | 成功认证Java用户: admin
2025-09-10 07:49:20.048 | INFO     | d7ce2e2ea74f4bf998d939d92407c767 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:49:20.050 | INFO     | d7ce2e2ea74f4bf998d939d92407c767 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 646.292ms
2025-09-10 07:54:19.406 | INFO     | e71c43ed57c24cc59cb50320a73d1763 | 成功认证Java用户: admin
2025-09-10 07:54:20.083 | INFO     | e71c43ed57c24cc59cb50320a73d1763 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:54:20.085 | INFO     | e71c43ed57c24cc59cb50320a73d1763 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 681.345ms
2025-09-10 07:59:19.411 | INFO     | 35ae80caf51d4c58a371f9bccd132717 | 成功认证Java用户: admin
2025-09-10 07:59:20.079 | INFO     | 35ae80caf51d4c58a371f9bccd132717 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 07:59:20.090 | INFO     | 35ae80caf51d4c58a371f9bccd132717 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 681.745ms
2025-09-10 08:04:19.410 | INFO     | c7934be2dda443af8c459fa0cd8fad5f | 成功认证Java用户: admin
2025-09-10 08:04:20.051 | INFO     | c7934be2dda443af8c459fa0cd8fad5f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:04:20.054 | INFO     | c7934be2dda443af8c459fa0cd8fad5f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 645.673ms
2025-09-10 08:09:19.411 | INFO     | 17db579f874c456b84a445c49aaf1feb | 成功认证Java用户: admin
2025-09-10 08:09:20.099 | INFO     | 17db579f874c456b84a445c49aaf1feb | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:09:20.101 | INFO     | 17db579f874c456b84a445c49aaf1feb | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 691.986ms
2025-09-10 08:14:19.407 | INFO     | 3cf2a7f5a76b463b93a5b7c0c830505e | 成功认证Java用户: admin
2025-09-10 08:14:20.157 | INFO     | 3cf2a7f5a76b463b93a5b7c0c830505e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:14:20.161 | INFO     | 3cf2a7f5a76b463b93a5b7c0c830505e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 756.053ms
2025-09-10 08:19:19.433 | INFO     | 0b7069b3061643329b0254f29b07794a | 成功认证Java用户: admin
2025-09-10 08:19:20.128 | INFO     | 0b7069b3061643329b0254f29b07794a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:19:20.130 | INFO     | 0b7069b3061643329b0254f29b07794a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 707.191ms
2025-09-10 08:24:19.404 | INFO     | 9529a051821043c1a2d5e85fd5f4c80f | 成功认证Java用户: admin
2025-09-10 08:24:20.082 | INFO     | 9529a051821043c1a2d5e85fd5f4c80f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:24:20.086 | INFO     | 9529a051821043c1a2d5e85fd5f4c80f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 684.855ms
2025-09-10 08:29:19.419 | INFO     | 369fc7c87c78447abd0ff497d002e8eb | 成功认证Java用户: admin
2025-09-10 08:29:20.103 | INFO     | 369fc7c87c78447abd0ff497d002e8eb | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:29:20.106 | INFO     | 369fc7c87c78447abd0ff497d002e8eb | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 689.354ms
2025-09-10 08:44:32.326 | INFO     | 2fb7d7fea4bf422aa08eadce1758159c | 成功认证Java用户: admin
2025-09-10 08:44:33.004 | INFO     | 2fb7d7fea4bf422aa08eadce1758159c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:44:33.008 | INFO     | 2fb7d7fea4bf422aa08eadce1758159c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 684.883ms
2025-09-10 08:49:33.407 | INFO     | 448511cce9094534a8418fce7bdf901e | 成功认证Java用户: admin
2025-09-10 08:49:34.067 | INFO     | 448511cce9094534a8418fce7bdf901e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:49:34.071 | INFO     | 448511cce9094534a8418fce7bdf901e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 666.312ms
2025-09-10 08:54:33.406 | INFO     | abfdea747510416e83be5496bcf1b6c4 | 成功认证Java用户: admin
2025-09-10 08:54:34.057 | INFO     | abfdea747510416e83be5496bcf1b6c4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 08:54:34.060 | INFO     | abfdea747510416e83be5496bcf1b6c4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 657.456ms
2025-09-10 09:48:18.297 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 09:54:50.269 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 09:55:29.641 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 09:58:20.066 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:02:36.143 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:03:10.380 | INFO     | 54cab4dcb1544a1b953f6fa50af2129a | 127.0.0.1       | GET      | 200    | /docs | 2.821ms
2025-09-10 10:03:12.483 | INFO     | 7f76e31bbbd040698c62a4bea0998758 | 127.0.0.1       | GET      | 200    | /openapi | 87.193ms
2025-09-10 10:11:17.789 | INFO     | b0b56bc4396b43d3b6258ffc92443048 | 成功认证Java用户: admin
2025-09-10 10:11:17.792 | INFO     | e075194dca4c4ec9980f3efb43441f68 | 成功认证Java用户: admin
2025-09-10 10:11:17.793 | INFO     | afca969c83a34464b757328d9b36fac9 | 成功认证Java用户: admin
2025-09-10 10:11:17.837 | INFO     | afca969c83a34464b757328d9b36fac9 | 权限检查通过: user_id=1, permission=knowledge:base:list, path=/api/iot/v1/knowledge-base/list
2025-09-10 10:11:17.838 | INFO     | e075194dca4c4ec9980f3efb43441f68 | 权限检查通过: user_id=1, permission=knowledge:base:stats, path=/api/iot/v1/knowledge-base/stats/overview
2025-09-10 10:11:18.138 | INFO     | b0b56bc4396b43d3b6258ffc92443048 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-09-10 10:11:18.140 | INFO     | b0b56bc4396b43d3b6258ffc92443048 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/health | 367.272ms
2025-09-10 10:11:18.142 | INFO     | afca969c83a34464b757328d9b36fac9 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-09-10 10:11:18.144 | INFO     | e075194dca4c4ec9980f3efb43441f68 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-09-10 10:11:18.145 | INFO     | afca969c83a34464b757328d9b36fac9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 363.225ms
2025-09-10 10:11:18.145 | INFO     | e075194dca4c4ec9980f3efb43441f68 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 363.829ms
2025-09-10 10:11:23.447 | INFO     | 3e6db15e1d34428595d9f623b2981a00 | 成功认证Java用户: admin
2025-09-10 10:11:23.448 | INFO     | 7348c3e5298e4f7686f95ff8bc7dc113 | 成功认证Java用户: admin
2025-09-10 10:11:23.449 | INFO     | e6c44a7e246f4c4b8eba2eaffc32d970 | 成功认证Java用户: admin
2025-09-10 10:11:23.461 | INFO     | 7348c3e5298e4f7686f95ff8bc7dc113 | 权限检查通过: user_id=1, permission=knowledge:base:stats, path=/api/iot/v1/knowledge-base/stats/overview
2025-09-10 10:11:23.462 | INFO     | e6c44a7e246f4c4b8eba2eaffc32d970 | 权限检查通过: user_id=1, permission=knowledge:base:list, path=/api/iot/v1/knowledge-base/list
2025-09-10 10:11:23.480 | INFO     | 3e6db15e1d34428595d9f623b2981a00 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-09-10 10:11:23.482 | INFO     | 7348c3e5298e4f7686f95ff8bc7dc113 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-09-10 10:11:23.483 | INFO     | e6c44a7e246f4c4b8eba2eaffc32d970 | HTTP Request: GET http://192.168.66.13:9222/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-09-10 10:11:23.483 | INFO     | 3e6db15e1d34428595d9f623b2981a00 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/health | 37.713ms
2025-09-10 10:11:23.484 | INFO     | 7348c3e5298e4f7686f95ff8bc7dc113 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.942ms
2025-09-10 10:11:23.484 | INFO     | e6c44a7e246f4c4b8eba2eaffc32d970 | 192.168.66.13   | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 38.576ms
2025-09-10 10:11:27.100 | INFO     | 4e2a6c903bef4b00bdcce026d7808e20 | 成功认证Java用户: admin
2025-09-10 10:11:27.495 | INFO     | 4e2a6c903bef4b00bdcce026d7808e20 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:11:27.497 | INFO     | 4e2a6c903bef4b00bdcce026d7808e20 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 398.851ms
2025-09-10 10:15:31.643 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 成功认证Java用户: admin
2025-09-10 10:15:31.652 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 10:15:32.338 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 698.021ms
2025-09-10 10:15:32.351 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:15:32.351 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | AI服务响应状态码: 200
2025-09-10 10:15:32.351 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 开始处理流式响应数据
2025-09-10 10:15:35.483 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 流式响应结束，原因: stop
2025-09-10 10:15:35.483 | INFO     | 0694c2d4686b4cf08acda0fe0aa6a602 | 流式响应完成，共处理 101 个数据块，总长度: 176
2025-09-10 10:16:28.403 | INFO     | e08bb6cd74fa4842a23d64b25a4f5b5f | 成功认证Java用户: admin
2025-09-10 10:16:28.818 | INFO     | e08bb6cd74fa4842a23d64b25a4f5b5f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:16:28.820 | INFO     | e08bb6cd74fa4842a23d64b25a4f5b5f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 419.646ms
2025-09-10 10:21:28.406 | INFO     | d501943fcc7c49fbb2583ad0a35a9948 | 成功认证Java用户: admin
2025-09-10 10:21:28.791 | INFO     | d501943fcc7c49fbb2583ad0a35a9948 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:21:28.792 | INFO     | d501943fcc7c49fbb2583ad0a35a9948 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 388.464ms
2025-09-10 10:21:37.258 | INFO     | 29594f50e29e47ec81635911192332eb | 成功认证Java用户: admin
2025-09-10 10:21:37.637 | INFO     | 29594f50e29e47ec81635911192332eb | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:21:37.639 | INFO     | 29594f50e29e47ec81635911192332eb | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 382.358ms
2025-09-10 10:22:00.445 | INFO     | 6d119c9a85f249ed947d64297c75435d | 成功认证Java用户: admin
2025-09-10 10:22:00.836 | INFO     | 6d119c9a85f249ed947d64297c75435d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:22:00.837 | INFO     | 6d119c9a85f249ed947d64297c75435d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.879ms
2025-09-10 10:22:24.123 | INFO     | 012f83e9db004224884de2ee0c148a1d | 成功认证Java用户: admin
2025-09-10 10:22:24.522 | INFO     | 012f83e9db004224884de2ee0c148a1d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:22:24.523 | INFO     | 012f83e9db004224884de2ee0c148a1d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 401.543ms
2025-09-10 10:24:22.110 | INFO     | 4ff09d81a81541e18d66630a0ec1362d | 成功认证Java用户: admin
2025-09-10 10:24:22.498 | INFO     | 4ff09d81a81541e18d66630a0ec1362d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:24:22.500 | INFO     | 4ff09d81a81541e18d66630a0ec1362d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.146ms
2025-09-10 10:25:39.160 | INFO     | 43aedaf21b4946c08a035387c1c32ff8 | 成功认证Java用户: admin
2025-09-10 10:25:39.690 | INFO     | 43aedaf21b4946c08a035387c1c32ff8 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:25:39.694 | INFO     | 43aedaf21b4946c08a035387c1c32ff8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 536.676ms
2025-09-10 10:26:11.092 | INFO     | 849af37fccd0409da50141e91db9a4b6 | 成功认证Java用户: admin
2025-09-10 10:26:11.531 | INFO     | 849af37fccd0409da50141e91db9a4b6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:26:11.533 | INFO     | 849af37fccd0409da50141e91db9a4b6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 443.296ms
2025-09-10 10:26:28.409 | INFO     | 557381ca4fa048b79237c6bce915f504 | 成功认证Java用户: admin
2025-09-10 10:26:28.822 | INFO     | 557381ca4fa048b79237c6bce915f504 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:26:28.824 | INFO     | 557381ca4fa048b79237c6bce915f504 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 415.942ms
2025-09-10 10:26:38.405 | INFO     | 280593b69781492db025e0ea3c8d4f6d | 成功认证Java用户: admin
2025-09-10 10:26:38.785 | INFO     | 280593b69781492db025e0ea3c8d4f6d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:26:38.787 | INFO     | 280593b69781492db025e0ea3c8d4f6d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 383.415ms
2025-09-10 10:27:01.413 | INFO     | 5fd084ffdee6446da498cf6d78c791d5 | 成功认证Java用户: admin
2025-09-10 10:27:01.817 | INFO     | 5fd084ffdee6446da498cf6d78c791d5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:27:01.819 | INFO     | 5fd084ffdee6446da498cf6d78c791d5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 407.146ms
2025-09-10 10:27:25.402 | INFO     | 77ff3e648b9a40319feda1911005d973 | 成功认证Java用户: admin
2025-09-10 10:27:25.828 | INFO     | 77ff3e648b9a40319feda1911005d973 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:27:25.830 | INFO     | 77ff3e648b9a40319feda1911005d973 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 429.653ms
2025-09-10 10:28:07.963 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:28:59.494 | INFO     | 55abc599bcf5426d9cff46bea73af500 | 127.0.0.1       | POST     | 401    | /api/iot/v1/chat/chat | 0.474ms
2025-09-10 10:29:09.965 | INFO     | d4f879f2c7a341ae87d8dc38e7766250 | 127.0.0.1       | GET      | 200    | /docs | 2.346ms
2025-09-10 10:29:10.064 | INFO     | 5df8c5b19522400e911ada53126422d6 | 127.0.0.1       | GET      | 200    | /openapi | 4.718ms
2025-09-10 10:29:22.512 | INFO     | c7bf9219d69149ce92127a4ac9c18ef5 | 成功认证Java用户: admin
2025-09-10 10:29:23.211 | INFO     | c7bf9219d69149ce92127a4ac9c18ef5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:29:23.214 | INFO     | c7bf9219d69149ce92127a4ac9c18ef5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 703.316ms
2025-09-10 10:29:25.010 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 成功认证Java用户: admin
2025-09-10 10:29:25.013 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 10:29:25.700 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 691.262ms
2025-09-10 10:29:25.713 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:29:25.713 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | AI服务响应状态码: 200
2025-09-10 10:29:25.714 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 开始处理流式响应数据
2025-09-10 10:29:45.568 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 流式响应结束，原因: stop
2025-09-10 10:29:45.569 | INFO     | 879f66d6b5fe427a8572cd727e4a4649 | 流式响应完成，共处理 613 个数据块，总长度: 1070
2025-09-10 10:29:57.204 | INFO     | f706c5459a2949b9b83a36f8d9749867 | 成功认证Java用户: admin
2025-09-10 10:29:57.206 | INFO     | f706c5459a2949b9b83a36f8d9749867 | 已清空用户 1 的会话 chat_session_1757471171063_cddth1gb77 历史记录
2025-09-10 10:29:57.206 | INFO     | f706c5459a2949b9b83a36f8d9749867 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757471171063_cddth1gb77 | 4.033ms
2025-09-10 10:30:14.098 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 成功认证Java用户: admin
2025-09-10 10:30:14.102 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 10:30:14.778 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 681.443ms
2025-09-10 10:30:14.790 | INFO     | 98c7af478d0744b0be6faa024f7de199 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:30:14.790 | INFO     | 98c7af478d0744b0be6faa024f7de199 | AI服务响应状态码: 200
2025-09-10 10:30:14.791 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 开始处理流式响应数据
2025-09-10 10:30:26.822 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 流式响应结束，原因: stop
2025-09-10 10:30:26.822 | INFO     | 98c7af478d0744b0be6faa024f7de199 | 流式响应完成，共处理 374 个数据块，总长度: 634
2025-09-10 10:30:30.105 | INFO     | 5b6d88fc11164669af20b4d3dc51b6e0 | 成功认证Java用户: admin
2025-09-10 10:30:30.107 | INFO     | 5b6d88fc11164669af20b4d3dc51b6e0 | 已清空用户 1 的会话 chat_session_1757471397212_0z782q7xtjw 历史记录
2025-09-10 10:30:30.107 | INFO     | 5b6d88fc11164669af20b4d3dc51b6e0 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757471397212_0z782q7xtjw | 3.757ms
2025-09-10 10:30:39.706 | INFO     | 45f264f0a6864f208263da7508aaa8b1 | 成功认证Java用户: admin
2025-09-10 10:30:40.413 | INFO     | 45f264f0a6864f208263da7508aaa8b1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:30:40.416 | INFO     | 45f264f0a6864f208263da7508aaa8b1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 711.462ms
2025-09-10 10:30:42.120 | INFO     | d08948cd502f4207b0266371925005e9 | 成功认证Java用户: admin
2025-09-10 10:30:42.123 | INFO     | d08948cd502f4207b0266371925005e9 | 已清空用户 1 的会话 chat_session_1757471430116_l6ms115j60s 历史记录
2025-09-10 10:30:42.123 | INFO     | d08948cd502f4207b0266371925005e9 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757471430116_l6ms115j60s | 4.080ms
2025-09-10 10:31:12.413 | INFO     | fbb605e88ef74c74ad1f0882cc739fba | 成功认证Java用户: admin
2025-09-10 10:31:12.789 | INFO     | fbb605e88ef74c74ad1f0882cc739fba | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:31:12.791 | INFO     | fbb605e88ef74c74ad1f0882cc739fba | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 380.151ms
2025-09-10 10:31:28.403 | INFO     | 731c26355d6049e0ae99a9fa0d1c29ff | 成功认证Java用户: admin
2025-09-10 10:31:28.851 | INFO     | 731c26355d6049e0ae99a9fa0d1c29ff | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:31:28.853 | INFO     | 731c26355d6049e0ae99a9fa0d1c29ff | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 451.046ms
2025-09-10 10:31:38.414 | INFO     | cab4ade0b44f438cafb1399f88852b1a | 成功认证Java用户: admin
2025-09-10 10:31:38.803 | INFO     | cab4ade0b44f438cafb1399f88852b1a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:31:38.804 | INFO     | cab4ade0b44f438cafb1399f88852b1a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 391.319ms
2025-09-10 10:31:41.575 | INFO     | e2f83b41a49342a3a8dcf5a6c656b144 | 成功认证Java用户: admin
2025-09-10 10:31:41.578 | INFO     | e2f83b41a49342a3a8dcf5a6c656b144 | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat | 3.267ms
2025-09-10 10:32:00.497 | INFO     | 6fc4f4e8548c4be8be57553936fc8abe | 成功认证Java用户: admin
2025-09-10 10:32:00.500 | INFO     | 6fc4f4e8548c4be8be57553936fc8abe | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat | 4.430ms
2025-09-10 10:32:01.410 | INFO     | e51ebac5e5e4463f84dd01032c24170c | 成功认证Java用户: admin
2025-09-10 10:32:01.793 | INFO     | e51ebac5e5e4463f84dd01032c24170c | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:32:01.794 | INFO     | e51ebac5e5e4463f84dd01032c24170c | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.411ms
2025-09-10 10:32:25.398 | INFO     | 2018dd81811a4c809a4c32f909cd7ec2 | 成功认证Java用户: admin
2025-09-10 10:32:25.790 | INFO     | 2018dd81811a4c809a4c32f909cd7ec2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:32:25.792 | INFO     | 2018dd81811a4c809a4c32f909cd7ec2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 394.945ms
2025-09-10 10:34:23.411 | INFO     | fe1f94b738d74d519604d15183ab6aa9 | 成功认证Java用户: admin
2025-09-10 10:34:23.809 | INFO     | fe1f94b738d74d519604d15183ab6aa9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:34:23.811 | INFO     | fe1f94b738d74d519604d15183ab6aa9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 401.539ms
2025-09-10 10:35:40.408 | INFO     | 73f87a1dc1d2461da213fce836b4c828 | 成功认证Java用户: admin
2025-09-10 10:35:40.792 | INFO     | 73f87a1dc1d2461da213fce836b4c828 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:35:40.796 | INFO     | 73f87a1dc1d2461da213fce836b4c828 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 391.047ms
2025-09-10 10:36:11.549 | INFO     | c096f68bb48f41afa97506063f1d26b1 | 成功认证Java用户: admin
2025-09-10 10:36:11.923 | INFO     | c096f68bb48f41afa97506063f1d26b1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:36:11.925 | INFO     | c096f68bb48f41afa97506063f1d26b1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 377.818ms
2025-09-10 10:36:27.512 | INFO     | ee039be8649841fb96d109ec253b5fc5 | 成功认证Java用户: admin
2025-09-10 10:36:28.217 | INFO     | ee039be8649841fb96d109ec253b5fc5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:36:28.221 | INFO     | ee039be8649841fb96d109ec253b5fc5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 710.509ms
2025-09-10 10:36:38.402 | INFO     | 8f740b0f52b846018784beefb37dc47e | 成功认证Java用户: admin
2025-09-10 10:36:38.784 | INFO     | 8f740b0f52b846018784beefb37dc47e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:36:38.786 | INFO     | 8f740b0f52b846018784beefb37dc47e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.067ms
2025-09-10 10:37:01.404 | INFO     | 5a2c54042c244562ab7da5736b49143e | 成功认证Java用户: admin
2025-09-10 10:37:01.795 | INFO     | 5a2c54042c244562ab7da5736b49143e | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:37:01.797 | INFO     | 5a2c54042c244562ab7da5736b49143e | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 393.923ms
2025-09-10 10:37:25.412 | INFO     | 6f854da69a2d4b63b595e6e043b57105 | 成功认证Java用户: admin
2025-09-10 10:37:25.946 | INFO     | 6f854da69a2d4b63b595e6e043b57105 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:37:25.950 | INFO     | 6f854da69a2d4b63b595e6e043b57105 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 539.353ms
2025-09-10 10:39:23.411 | INFO     | 226d9e37623b48a2883fa5d2ba41fd22 | 成功认证Java用户: admin
2025-09-10 10:39:23.791 | INFO     | 226d9e37623b48a2883fa5d2ba41fd22 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:39:23.793 | INFO     | 226d9e37623b48a2883fa5d2ba41fd22 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 384.360ms
2025-09-10 10:39:45.263 | INFO     | 88abaef1d3a148f1be2966903f4cb908 | 成功认证Java用户: admin
2025-09-10 10:39:45.264 | INFO     | 88abaef1d3a148f1be2966903f4cb908 | 127.0.0.1       | GET      | 404    | /api/iot/v1/tools/list | 3.352ms
2025-09-10 10:39:47.294 | INFO     | 54984ac4737b4e5e974b3233587ed7ed | 成功认证Java用户: admin
2025-09-10 10:39:47.299 | INFO     | 54984ac4737b4e5e974b3233587ed7ed | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 6.129ms
2025-09-10 10:39:49.345 | INFO     | ec9c50299ca6469b96fc3fb56391cae9 | 成功认证Java用户: admin
2025-09-10 10:39:49.347 | INFO     | ec9c50299ca6469b96fc3fb56391cae9 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 3.397ms
2025-09-10 10:39:51.386 | INFO     | c5932a72aeca4effafab42c84d9c4f88 | 成功认证Java用户: admin
2025-09-10 10:39:51.389 | INFO     | c5932a72aeca4effafab42c84d9c4f88 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 3.795ms
2025-09-10 10:40:40.409 | INFO     | 3d851107bb2042dfbc7e8e76216f73d9 | 成功认证Java用户: admin
2025-09-10 10:40:40.815 | INFO     | 3d851107bb2042dfbc7e8e76216f73d9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:40:40.818 | INFO     | 3d851107bb2042dfbc7e8e76216f73d9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 410.478ms
2025-09-10 10:41:12.410 | INFO     | 774ce5eea5b24d2499d0c638a51730ee | 成功认证Java用户: admin
2025-09-10 10:41:12.790 | INFO     | 774ce5eea5b24d2499d0c638a51730ee | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:41:12.792 | INFO     | 774ce5eea5b24d2499d0c638a51730ee | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 384.167ms
2025-09-10 10:41:28.407 | INFO     | 3ff4eb92d27a4b06a85162f03959b1c6 | 成功认证Java用户: admin
2025-09-10 10:41:28.792 | INFO     | 3ff4eb92d27a4b06a85162f03959b1c6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:41:28.794 | INFO     | 3ff4eb92d27a4b06a85162f03959b1c6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 388.320ms
2025-09-10 10:41:38.408 | INFO     | 5d27654cecea4c4792e9a7c8824343a9 | 成功认证Java用户: admin
2025-09-10 10:41:38.791 | INFO     | 5d27654cecea4c4792e9a7c8824343a9 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:41:38.794 | INFO     | 5d27654cecea4c4792e9a7c8824343a9 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.147ms
2025-09-10 10:42:01.409 | INFO     | 83074c9a0b2d4f18922ecc8604fefd56 | 成功认证Java用户: admin
2025-09-10 10:42:01.813 | INFO     | 83074c9a0b2d4f18922ecc8604fefd56 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:42:01.815 | INFO     | 83074c9a0b2d4f18922ecc8604fefd56 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.122ms
2025-09-10 10:42:25.401 | INFO     | afa7c200843846c98313f8533a18b793 | 成功认证Java用户: admin
2025-09-10 10:42:25.793 | INFO     | afa7c200843846c98313f8533a18b793 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:42:25.794 | INFO     | afa7c200843846c98313f8533a18b793 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 395.603ms
2025-09-10 10:44:23.406 | INFO     | 970246bcfa2e49eb955115e12bbefc28 | 成功认证Java用户: admin
2025-09-10 10:44:23.781 | INFO     | 970246bcfa2e49eb955115e12bbefc28 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:44:23.783 | INFO     | 970246bcfa2e49eb955115e12bbefc28 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 379.334ms
2025-09-10 10:45:40.409 | INFO     | 8d09d2318d054cd3b20f00cb61c550e8 | 成功认证Java用户: admin
2025-09-10 10:45:40.811 | INFO     | 8d09d2318d054cd3b20f00cb61c550e8 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:45:40.813 | INFO     | 8d09d2318d054cd3b20f00cb61c550e8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 405.420ms
2025-09-10 10:46:12.410 | INFO     | e17dd6ba93a442499e3da1cb8eb509a0 | 成功认证Java用户: admin
2025-09-10 10:46:12.786 | INFO     | e17dd6ba93a442499e3da1cb8eb509a0 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:46:12.788 | INFO     | e17dd6ba93a442499e3da1cb8eb509a0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 379.563ms
2025-09-10 10:46:24.851 | INFO     | ebb8169c0f0e4b74a72758adae277789 | 成功认证Java用户: admin
2025-09-10 10:46:24.853 | INFO     | ebb8169c0f0e4b74a72758adae277789 | 127.0.0.1       | GET      | 404    | /api/iot/v1/tools/list | 4.242ms
2025-09-10 10:46:26.886 | INFO     | 3542dc0775f0452f8450d38384460119 | 成功认证Java用户: admin
2025-09-10 10:46:26.889 | INFO     | 3542dc0775f0452f8450d38384460119 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 5.119ms
2025-09-10 10:46:28.938 | INFO     | cca27205719846f48c5866fdeda3fe24 | 成功认证Java用户: admin
2025-09-10 10:46:28.940 | INFO     | cca27205719846f48c5866fdeda3fe24 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 3.572ms
2025-09-10 10:46:30.995 | INFO     | c88ab5ce1cf04300b201f9d184f4fb25 | 成功认证Java用户: admin
2025-09-10 10:46:30.998 | INFO     | c88ab5ce1cf04300b201f9d184f4fb25 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 5.862ms
2025-09-10 10:46:38.405 | INFO     | f36df8a7c9fa4cd4aa571ede53215cb2 | 成功认证Java用户: admin
2025-09-10 10:46:38.780 | INFO     | f36df8a7c9fa4cd4aa571ede53215cb2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:46:38.782 | INFO     | f36df8a7c9fa4cd4aa571ede53215cb2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 378.422ms
2025-09-10 10:47:06.226 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:47:19.423 | INFO     | dec9d86149f246b4962eb0c7a63bc8f1 | 成功认证Java用户: admin
2025-09-10 10:47:19.839 | INFO     | dec9d86149f246b4962eb0c7a63bc8f1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:47:19.841 | INFO     | dec9d86149f246b4962eb0c7a63bc8f1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 424.095ms
2025-09-10 10:47:22.669 | INFO     | 2d64046e78ce4b0db580b92828062448 | 成功认证Java用户: admin
2025-09-10 10:47:22.671 | INFO     | 2d64046e78ce4b0db580b92828062448 | 127.0.0.1       | GET      | 404    | /api/iot/v1/tools/list | 4.174ms
2025-09-10 10:47:24.733 | INFO     | b24898462efa4663a51e2bba3ffd369f | 成功认证Java用户: admin
2025-09-10 10:47:24.738 | INFO     | b24898462efa4663a51e2bba3ffd369f | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:47:24.738 | INFO     | b24898462efa4663a51e2bba3ffd369f | 初始化LangGraph智能体服务...
2025-09-10 10:47:24.738 | INFO     | b24898462efa4663a51e2bba3ffd369f | 加载了 3 个工具
2025-09-10 10:47:24.741 | INFO     | b24898462efa4663a51e2bba3ffd369f | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:47:24.741 | INFO     | b24898462efa4663a51e2bba3ffd369f | LangGraph智能体服务初始化完成
2025-09-10 10:47:25.613 | INFO     | a9292e4b5f7a47f0a07204bf94b5517d | 成功认证Java用户: admin
2025-09-10 10:47:25.993 | INFO     | a9292e4b5f7a47f0a07204bf94b5517d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:47:25.995 | INFO     | a9292e4b5f7a47f0a07204bf94b5517d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 383.247ms
2025-09-10 10:47:26.006 | INFO     | b24898462efa4663a51e2bba3ffd369f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:47:26.007 | INFO     | b24898462efa4663a51e2bba3ffd369f | LangGraph智能体执行完成: 5685b14b-6c7f-4b04-8a25-821ea40c310b
2025-09-10 10:47:26.017 | INFO     | b24898462efa4663a51e2bba3ffd369f | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 1284.468ms
2025-09-10 10:47:28.049 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 成功认证Java用户: admin
2025-09-10 10:47:28.051 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 用户 1 发起统一聊天: 查询Vue.js的生命周期钩子函数... (模式: agent)
2025-09-10 10:47:28.051 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 初始化LangGraph智能体服务...
2025-09-10 10:47:28.051 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 加载了 3 个工具
2025-09-10 10:47:28.053 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:47:28.053 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | LangGraph智能体服务初始化完成
2025-09-10 10:47:28.072 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:47:28.074 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | LangGraph智能体执行完成: 39b1544b-388c-4674-9e36-8f5c277d74b2
2025-09-10 10:47:28.074 | INFO     | 4d614edf174f4e6a8d2d3d16d131f0df | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 27.318ms
2025-09-10 10:47:30.119 | INFO     | 1661401f64cd43049f4e169322fdf4aa | 成功认证Java用户: admin
2025-09-10 10:47:30.120 | INFO     | 1661401f64cd43049f4e169322fdf4aa | 用户 1 发起统一聊天: 你好，请介绍一下你的功能... (模式: auto)
2025-09-10 10:47:30.500 | INFO     | 1661401f64cd43049f4e169322fdf4aa | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-09-10 10:47:30.888 | INFO     | 1661401f64cd43049f4e169322fdf4aa | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-09-10 10:47:30.890 | INFO     | 1661401f64cd43049f4e169322fdf4aa | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 773.083ms
2025-09-10 10:49:23.399 | INFO     | c063c3ebfdef4fa5be5f8ae83e7a786f | 成功认证Java用户: admin
2025-09-10 10:49:23.820 | INFO     | c063c3ebfdef4fa5be5f8ae83e7a786f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:49:23.821 | INFO     | c063c3ebfdef4fa5be5f8ae83e7a786f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 424.570ms
2025-09-10 10:50:12.261 | INFO     | beb0beb3ea094aef97f51d404b371e36 | 成功认证Java用户: admin
2025-09-10 10:50:12.263 | INFO     | beb0beb3ea094aef97f51d404b371e36 | 127.0.0.1       | GET      | 404    | /api/iot/v1/tools/list | 3.595ms
2025-09-10 10:50:14.310 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 成功认证Java用户: admin
2025-09-10 10:50:14.314 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:50:14.314 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 初始化LangGraph智能体服务...
2025-09-10 10:50:14.314 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 加载了 3 个工具
2025-09-10 10:50:14.316 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:50:14.316 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | LangGraph智能体服务初始化完成
2025-09-10 10:50:14.336 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:50:14.337 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | LangGraph智能体执行完成: d0591fa8-9d21-4530-8aa3-c77ba8f1c07d
2025-09-10 10:50:14.338 | INFO     | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 30.049ms
2025-09-10 10:50:16.405 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 成功认证Java用户: admin
2025-09-10 10:50:16.407 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 用户 1 发起统一聊天: 查询Vue.js的生命周期钩子函数... (模式: agent)
2025-09-10 10:50:16.407 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 初始化LangGraph智能体服务...
2025-09-10 10:50:16.408 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 加载了 3 个工具
2025-09-10 10:50:16.409 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:50:16.410 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | LangGraph智能体服务初始化完成
2025-09-10 10:50:16.428 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:50:16.429 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | LangGraph智能体执行完成: a3a7925a-7d24-43f8-8fb5-0e34b9f46d74
2025-09-10 10:50:16.430 | INFO     | 8627fd1a179d4bb6a3499e8678e7dc76 | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 26.494ms
2025-09-10 10:50:18.476 | INFO     | 523576b059764124bd01b2cec9b3745e | 成功认证Java用户: admin
2025-09-10 10:50:18.478 | INFO     | 523576b059764124bd01b2cec9b3745e | 用户 1 发起统一聊天: 你好，请介绍一下你的功能... (模式: auto)
2025-09-10 10:50:18.859 | INFO     | 523576b059764124bd01b2cec9b3745e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-09-10 10:50:19.241 | INFO     | 523576b059764124bd01b2cec9b3745e | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 404 Not Found"
2025-09-10 10:50:19.243 | INFO     | 523576b059764124bd01b2cec9b3745e | 127.0.0.1       | POST     | 500    | /api/iot/v1/chat/chat | 768.580ms
2025-09-10 10:50:40.404 | INFO     | b196c81597934adead3adfcd6d4eed17 | 成功认证Java用户: admin
2025-09-10 10:50:40.784 | INFO     | b196c81597934adead3adfcd6d4eed17 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:50:40.786 | INFO     | b196c81597934adead3adfcd6d4eed17 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 383.246ms
2025-09-10 10:53:02.558 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:53:27.378 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 成功认证Java用户: admin
2025-09-10 10:53:27.383 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:53:27.383 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 初始化LangGraph智能体服务...
2025-09-10 10:53:27.383 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 加载了 3 个工具
2025-09-10 10:53:27.385 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:53:27.386 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | LangGraph智能体服务初始化完成
2025-09-10 10:53:28.323 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:53:28.325 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | LangGraph智能体执行完成: b8acd284-dc9e-4e82-9338-d44dd13d61c5
2025-09-10 10:53:28.327 | INFO     | a10e535d08394683aaf6d9f07f0e5b01 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 955.879ms
2025-09-10 10:54:23.408 | INFO     | 769d0fdf358e4fd1ac53072020accaaf | 成功认证Java用户: admin
2025-09-10 10:54:23.785 | INFO     | 769d0fdf358e4fd1ac53072020accaaf | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:54:23.787 | INFO     | 769d0fdf358e4fd1ac53072020accaaf | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 380.816ms
2025-09-10 10:54:29.653 | INFO     | 04509ae667754c68b310906b51c3f9fb | 成功认证Java用户: admin
2025-09-10 10:54:29.656 | INFO     | 04509ae667754c68b310906b51c3f9fb | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:54:29.656 | INFO     | 04509ae667754c68b310906b51c3f9fb | 初始化LangGraph智能体服务...
2025-09-10 10:54:29.656 | INFO     | 04509ae667754c68b310906b51c3f9fb | 加载了 3 个工具
2025-09-10 10:54:29.658 | INFO     | 04509ae667754c68b310906b51c3f9fb | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:54:29.658 | INFO     | 04509ae667754c68b310906b51c3f9fb | LangGraph智能体服务初始化完成
2025-09-10 10:54:29.678 | INFO     | 04509ae667754c68b310906b51c3f9fb | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:54:29.681 | INFO     | 04509ae667754c68b310906b51c3f9fb | LangGraph智能体执行完成: cd079743-f029-40de-a8d3-c87078aa7aaa
2025-09-10 10:54:29.682 | INFO     | 04509ae667754c68b310906b51c3f9fb | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 30.051ms
2025-09-10 10:55:07.753 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:55:40.416 | INFO     | 900464c94cab4e3fbe7d8c8895d6c8d0 | 成功认证Java用户: admin
2025-09-10 10:55:40.815 | INFO     | 900464c94cab4e3fbe7d8c8895d6c8d0 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:55:40.817 | INFO     | 900464c94cab4e3fbe7d8c8895d6c8d0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.928ms
2025-09-10 10:56:12.404 | INFO     | f6003f10daa84d459f80180e8bbf6240 | 成功认证Java用户: admin
2025-09-10 10:56:12.782 | INFO     | f6003f10daa84d459f80180e8bbf6240 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:56:12.785 | INFO     | f6003f10daa84d459f80180e8bbf6240 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 383.402ms
2025-09-10 10:56:43.278 | INFO     | 7a1bb6d049b74e4c8047350cdf6655fe | 成功认证Java用户: admin
2025-09-10 10:56:43.682 | INFO     | 7a1bb6d049b74e4c8047350cdf6655fe | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:56:43.684 | INFO     | 7a1bb6d049b74e4c8047350cdf6655fe | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.400ms
2025-09-10 10:56:43.688 | INFO     | ba2f09de33bf4681930d0c21f6e793c4 | 成功认证Java用户: admin
2025-09-10 10:56:44.089 | INFO     | ba2f09de33bf4681930d0c21f6e793c4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:56:44.091 | INFO     | ba2f09de33bf4681930d0c21f6e793c4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 405.476ms
2025-09-10 10:56:44.352 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 成功认证Java用户: admin
2025-09-10 10:56:44.357 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:56:44.357 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 初始化LangGraph智能体服务...
2025-09-10 10:56:44.357 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 加载了 3 个工具
2025-09-10 10:56:44.359 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:56:44.360 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | LangGraph智能体服务初始化完成
2025-09-10 10:56:45.304 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-09-10 10:56:45.306 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | LangGraph智能体执行完成: c3ba3b95-7ae5-4374-8c98-e9359ad8dc6c
2025-09-10 10:56:45.308 | INFO     | ad6a6e2f3902488fb6c97448ba593ac6 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 956.929ms
2025-09-10 10:57:01.404 | INFO     | 735b3cd05f7844d8a637679e2d801626 | 成功认证Java用户: admin
2025-09-10 10:57:02.128 | INFO     | 735b3cd05f7844d8a637679e2d801626 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:57:02.131 | INFO     | 735b3cd05f7844d8a637679e2d801626 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 729.562ms
2025-09-10 10:57:25.406 | INFO     | 335239c3cde04d1faa7e14c5ffaf8df0 | 成功认证Java用户: admin
2025-09-10 10:57:25.795 | INFO     | 335239c3cde04d1faa7e14c5ffaf8df0 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 10:57:25.797 | INFO     | 335239c3cde04d1faa7e14c5ffaf8df0 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.046ms
2025-09-10 10:58:28.652 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 10:59:46.546 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 成功认证Java用户: admin
2025-09-10 10:59:46.550 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 10:59:46.550 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 初始化LangGraph智能体服务...
2025-09-10 10:59:46.550 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 加载了 3 个工具
2025-09-10 10:59:46.553 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 10:59:46.553 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | LangGraph智能体服务初始化完成
2025-09-10 10:59:51.931 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:59:57.015 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 10:59:57.017 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | LangGraph智能体执行完成: ce02a334-4166-4f3d-b474-fa6f5ff1d413
2025-09-10 10:59:57.018 | INFO     | 55abcc0ebc9c4da9b30dcbd365282f1f | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 10479.854ms
2025-09-10 11:00:19.412 | INFO     | 431acf83dcaa4305aae6aafa957e99b1 | 成功认证Java用户: admin
2025-09-10 11:00:19.793 | INFO     | 431acf83dcaa4305aae6aafa957e99b1 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:00:19.795 | INFO     | 431acf83dcaa4305aae6aafa957e99b1 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 385.824ms
2025-09-10 11:00:39.707 | INFO     | 5a22a16a95554163918cc4df697ff329 | 成功认证Java用户: admin
2025-09-10 11:00:40.413 | INFO     | 5a22a16a95554163918cc4df697ff329 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:00:40.415 | INFO     | 5a22a16a95554163918cc4df697ff329 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 710.837ms
2025-09-10 11:00:47.347 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 成功认证Java用户: admin
2025-09-10 11:00:47.354 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:00:48.053 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 707.942ms
2025-09-10 11:00:48.064 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:00:48.064 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | AI服务响应状态码: 200
2025-09-10 11:00:48.066 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 开始处理流式响应数据
2025-09-10 11:00:55.083 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 流式响应结束，原因: stop
2025-09-10 11:00:55.084 | INFO     | 74c373fbfa8a42f29d17592d201a04d1 | 流式响应完成，共处理 220 个数据块，总长度: 388
2025-09-10 11:01:12.412 | INFO     | 8bef79c032de4c2192a3f4c354748392 | 成功认证Java用户: admin
2025-09-10 11:01:12.786 | INFO     | 8bef79c032de4c2192a3f4c354748392 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:01:12.788 | INFO     | 8bef79c032de4c2192a3f4c354748392 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 376.497ms
2025-09-10 11:01:28.409 | INFO     | ad29500958e642d191c80dd7469a9a09 | 成功认证Java用户: admin
2025-09-10 11:01:28.798 | INFO     | ad29500958e642d191c80dd7469a9a09 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:01:28.800 | INFO     | ad29500958e642d191c80dd7469a9a09 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.115ms
2025-09-10 11:01:38.402 | INFO     | 6eda6045bfcf4dfa8dd31ebe2e59bba2 | 成功认证Java用户: admin
2025-09-10 11:01:38.788 | INFO     | 6eda6045bfcf4dfa8dd31ebe2e59bba2 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:01:38.789 | INFO     | 6eda6045bfcf4dfa8dd31ebe2e59bba2 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 388.852ms
2025-09-10 11:02:01.404 | INFO     | 571ef0e52c674f3f8766715b4ac95d86 | 成功认证Java用户: admin
2025-09-10 11:02:01.789 | INFO     | 571ef0e52c674f3f8766715b4ac95d86 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:02:01.790 | INFO     | 571ef0e52c674f3f8766715b4ac95d86 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 387.356ms
2025-09-10 11:02:44.066 | INFO     | 7c3fa0250f48440db6a692d5adf395ec | 成功认证Java用户: admin
2025-09-10 11:02:44.448 | INFO     | 7c3fa0250f48440db6a692d5adf395ec | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:02:44.450 | INFO     | 7c3fa0250f48440db6a692d5adf395ec | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.716ms
2025-09-10 11:05:19.412 | INFO     | bef25bcb8e624aa6a4050dd571b96a59 | 成功认证Java用户: admin
2025-09-10 11:05:19.825 | INFO     | bef25bcb8e624aa6a4050dd571b96a59 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:05:19.827 | INFO     | bef25bcb8e624aa6a4050dd571b96a59 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 417.860ms
2025-09-10 11:06:19.410 | INFO     | 28605084a3a7489f94f8dc86e3d95288 | 成功认证Java用户: admin
2025-09-10 11:06:19.905 | INFO     | 28605084a3a7489f94f8dc86e3d95288 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:06:19.908 | INFO     | 28605084a3a7489f94f8dc86e3d95288 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 500.557ms
2025-09-10 11:06:19.911 | INFO     | a397820f1e2c46c6bb45169ca5f35271 | 成功认证Java用户: admin
2025-09-10 11:06:20.318 | INFO     | a397820f1e2c46c6bb45169ca5f35271 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:06:20.321 | INFO     | a397820f1e2c46c6bb45169ca5f35271 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 412.158ms
2025-09-10 11:07:19.412 | INFO     | c669751569e742bea80cbb8dce5a41f4 | 成功认证Java用户: admin
2025-09-10 11:07:19.842 | INFO     | c669751569e742bea80cbb8dce5a41f4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:07:19.844 | INFO     | c669751569e742bea80cbb8dce5a41f4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 433.813ms
2025-09-10 11:07:19.848 | INFO     | 4356276e2d384c27a6f4560532be9cd6 | 成功认证Java用户: admin
2025-09-10 11:07:20.253 | INFO     | 4356276e2d384c27a6f4560532be9cd6 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:07:20.254 | INFO     | 4356276e2d384c27a6f4560532be9cd6 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.798ms
2025-09-10 11:07:20.258 | INFO     | dafee0c02053428f9ec26e78b1ab35e3 | 成功认证Java用户: admin
2025-09-10 11:07:20.663 | INFO     | dafee0c02053428f9ec26e78b1ab35e3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:07:20.665 | INFO     | dafee0c02053428f9ec26e78b1ab35e3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 408.595ms
2025-09-10 11:08:19.402 | INFO     | 350236b1076d402abe94527dc9b1f7d8 | 成功认证Java用户: admin
2025-09-10 11:08:19.784 | INFO     | 350236b1076d402abe94527dc9b1f7d8 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:08:19.786 | INFO     | 350236b1076d402abe94527dc9b1f7d8 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 386.538ms
2025-09-10 11:08:54.583 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:09:11.546 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 成功认证Java用户: admin
2025-09-10 11:09:11.550 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:09:11.551 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 初始化LangGraph智能体服务...
2025-09-10 11:09:11.551 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 加载了 3 个工具
2025-09-10 11:09:11.553 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:09:11.554 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | LangGraph智能体服务初始化完成
2025-09-10 11:09:16.527 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:09:21.105 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:09:21.106 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | LangGraph智能体执行完成: 145dbf10-007d-4691-80b3-128a4a1f57b7
2025-09-10 11:09:21.107 | INFO     | d1f6df0964e84dff8700a17e4d0dd433 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 9567.661ms
2025-09-10 11:09:56.448 | INFO     | b8e544d5b4294ecfb368412c6c12dede | 成功认证Java用户: admin
2025-09-10 11:09:56.856 | INFO     | b8e544d5b4294ecfb368412c6c12dede | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:09:56.859 | INFO     | b8e544d5b4294ecfb368412c6c12dede | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 414.021ms
2025-09-10 11:09:58.371 | INFO     | bdedab717f7b446ea9c715442f706673 | 成功认证Java用户: admin
2025-09-10 11:09:59.051 | INFO     | bdedab717f7b446ea9c715442f706673 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:09:59.053 | INFO     | bdedab717f7b446ea9c715442f706673 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 683.752ms
2025-09-10 11:10:01.852 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 成功认证Java用户: admin
2025-09-10 11:10:01.859 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:10:02.560 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 708.634ms
2025-09-10 11:10:02.572 | INFO     | cff3e6ab434444d085b10d194d3fff42 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:10:02.572 | INFO     | cff3e6ab434444d085b10d194d3fff42 | AI服务响应状态码: 200
2025-09-10 11:10:02.573 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 开始处理流式响应数据
2025-09-10 11:10:07.377 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 流式响应结束，原因: stop
2025-09-10 11:10:07.377 | INFO     | cff3e6ab434444d085b10d194d3fff42 | 流式响应完成，共处理 151 个数据块，总长度: 262
2025-09-10 11:10:16.515 | INFO     | 698987d3bb33431b9e8014ef69e2be04 | 成功认证Java用户: admin
2025-09-10 11:10:16.517 | INFO     | 698987d3bb33431b9e8014ef69e2be04 | 已清空用户 1 的会话 chat_session_1757473798343_gmrfsyn9qxn 历史记录
2025-09-10 11:10:16.517 | INFO     | 698987d3bb33431b9e8014ef69e2be04 | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757473798343_gmrfsyn9qxn | 4.508ms
2025-09-10 11:10:20.503 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 成功认证Java用户: admin
2025-09-10 11:10:20.508 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:10:21.190 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 688.303ms
2025-09-10 11:10:21.202 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:10:21.202 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | AI服务响应状态码: 200
2025-09-10 11:10:21.203 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 开始处理流式响应数据
2025-09-10 11:10:29.052 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 流式响应结束，原因: stop
2025-09-10 11:10:29.053 | INFO     | 276615beb6c84b59bd52e23b5ce0a9f3 | 流式响应完成，共处理 246 个数据块，总长度: 430
2025-09-10 11:10:38.103 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 成功认证Java用户: admin
2025-09-10 11:10:38.106 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 开始调用AI服务流式接口: http://192.168.2.188:8888/v1/chat/completions
2025-09-10 11:10:38.790 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 192.168.66.13   | POST     | 200    | /api/iot/v1/ai/chat/stream | 688.875ms
2025-09-10 11:10:38.802 | INFO     | a033bad5e31e4fe68e7d487182716b89 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:10:38.802 | INFO     | a033bad5e31e4fe68e7d487182716b89 | AI服务响应状态码: 200
2025-09-10 11:10:38.803 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 开始处理流式响应数据
2025-09-10 11:10:39.720 | INFO     | bf1112cddbc4421b8a98ecdc3445adf5 | 成功认证Java用户: admin
2025-09-10 11:10:40.407 | INFO     | bf1112cddbc4421b8a98ecdc3445adf5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:10:40.412 | INFO     | bf1112cddbc4421b8a98ecdc3445adf5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 693.704ms
2025-09-10 11:10:48.527 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 流式响应结束，原因: stop
2025-09-10 11:10:48.528 | INFO     | a033bad5e31e4fe68e7d487182716b89 | 流式响应完成，共处理 303 个数据块，总长度: 557
2025-09-10 11:11:11.568 | INFO     | c857f7fadfda4278a29cf643b1d1ec44 | 成功认证Java用户: admin
2025-09-10 11:11:11.987 | INFO     | c857f7fadfda4278a29cf643b1d1ec44 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:11:11.989 | INFO     | c857f7fadfda4278a29cf643b1d1ec44 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 423.506ms
2025-09-10 11:11:28.404 | INFO     | 4368f48157ff4690b0cb96fc2885f6bd | 成功认证Java用户: admin
2025-09-10 11:11:28.794 | INFO     | 4368f48157ff4690b0cb96fc2885f6bd | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:11:28.795 | INFO     | 4368f48157ff4690b0cb96fc2885f6bd | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 392.566ms
2025-09-10 11:11:38.398 | INFO     | 79f94b62f73c49df9a5d0d4abdc00aa3 | 成功认证Java用户: admin
2025-09-10 11:11:38.792 | INFO     | 79f94b62f73c49df9a5d0d4abdc00aa3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:11:38.794 | INFO     | 79f94b62f73c49df9a5d0d4abdc00aa3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 397.430ms
2025-09-10 11:12:01.406 | INFO     | b731209c5b9a42dd867a3e7a76b99982 | 成功认证Java用户: admin
2025-09-10 11:12:01.800 | INFO     | b731209c5b9a42dd867a3e7a76b99982 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:12:01.803 | INFO     | b731209c5b9a42dd867a3e7a76b99982 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 398.002ms
2025-09-10 11:13:19.412 | INFO     | 09a8a3f4f4124a568fc95a889a07572a | 成功认证Java用户: admin
2025-09-10 11:13:19.823 | INFO     | 09a8a3f4f4124a568fc95a889a07572a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:13:19.825 | INFO     | 09a8a3f4f4124a568fc95a889a07572a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 416.403ms
2025-09-10 11:14:40.314 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:14:54.139 | INFO     | 492673091b954ec9b21d448328296317 | 成功认证Java用户: admin
2025-09-10 11:14:54.142 | INFO     | 492673091b954ec9b21d448328296317 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:14:54.143 | INFO     | 492673091b954ec9b21d448328296317 | 初始化LangGraph智能体服务...
2025-09-10 11:14:54.143 | INFO     | 492673091b954ec9b21d448328296317 | 加载了 3 个工具
2025-09-10 11:14:54.145 | INFO     | 492673091b954ec9b21d448328296317 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:14:54.146 | INFO     | 492673091b954ec9b21d448328296317 | LangGraph智能体服务初始化完成
2025-09-10 11:14:59.223 | INFO     | 492673091b954ec9b21d448328296317 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:14:59.408 | INFO     | 0d60b42c4e8d430ba7605696f8cb8448 | 成功认证Java用户: admin
2025-09-10 11:14:59.791 | INFO     | 0d60b42c4e8d430ba7605696f8cb8448 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:14:59.794 | INFO     | 0d60b42c4e8d430ba7605696f8cb8448 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 387.008ms
2025-09-10 11:15:03.127 | INFO     | 492673091b954ec9b21d448328296317 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:15:03.130 | INFO     | 492673091b954ec9b21d448328296317 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 8997.819ms
2025-09-10 11:15:19.403 | INFO     | bffed3e660944219b6733348b77bcd9d | 成功认证Java用户: admin
2025-09-10 11:15:19.842 | INFO     | bffed3e660944219b6733348b77bcd9d | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:15:19.844 | INFO     | bffed3e660944219b6733348b77bcd9d | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 442.868ms
2025-09-10 11:17:56.201 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:18:19.420 | INFO     | a1d5945a776f4a36b67b28d0a690e09a | 成功认证Java用户: admin
2025-09-10 11:18:19.842 | INFO     | a1d5945a776f4a36b67b28d0a690e09a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:18:19.843 | INFO     | a1d5945a776f4a36b67b28d0a690e09a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 430.488ms
2025-09-10 11:19:06.253 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:20:13.353 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:20:19.416 | INFO     | bcfe2cca93aa4b89bc9fff48c7971bf4 | 成功认证Java用户: admin
2025-09-10 11:20:19.821 | INFO     | bcfe2cca93aa4b89bc9fff48c7971bf4 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:20:19.823 | INFO     | bcfe2cca93aa4b89bc9fff48c7971bf4 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 414.965ms
2025-09-10 11:21:07.219 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:21:19.423 | INFO     | 6b909bb91287432bb66b96ed491c5da3 | 成功认证Java用户: admin
2025-09-10 11:21:19.832 | INFO     | 6b909bb91287432bb66b96ed491c5da3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:21:19.834 | INFO     | 6b909bb91287432bb66b96ed491c5da3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 418.324ms
2025-09-10 11:21:19.837 | INFO     | 1d6d0f8eab5f40e59f77633d55f0ca84 | 成功认证Java用户: admin
2025-09-10 11:21:20.218 | INFO     | 1d6d0f8eab5f40e59f77633d55f0ca84 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:21:20.219 | INFO     | 1d6d0f8eab5f40e59f77633d55f0ca84 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 384.358ms
2025-09-10 11:21:26.313 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 成功认证Java用户: admin
2025-09-10 11:21:26.318 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:21:26.318 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 初始化LangGraph智能体服务...
2025-09-10 11:21:26.318 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 加载了 3 个工具
2025-09-10 11:21:26.320 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:21:26.320 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | LangGraph智能体服务初始化完成
2025-09-10 11:21:32.036 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:21:34.129 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:21:34.132 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:21:34.132 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 11:21:34.132 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。这个计算是基本的加法，所以应该用calculator。
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-3665d30d7d784412a28dcd570ea39890', 'type': 'tool_call'}
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 提取的工具信息: name=name, args={'expression': '2 + 3'}, id=tool_1_0
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 成功添加工具调用信息: {'tool_name': 'name', 'tool_display_name': 'name', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T11:21:34.133580', 'end_time': '2025-09-10T11:21:34.133580', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'tool_1_0'}}
2025-09-10 11:21:34.133 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:21:34.134 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2加3，我应该用计算器工具。先调用calculator函数，表达式是2+3。然后得到结果5，直接告诉用户答案就行了。不需要其他步骤，很简单。
</think>

2 
2025-09-10 11:21:34.134 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:21:34.134 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | LangGraph智能体执行完成: 0752624d-2c85-49d0-bcf1-4b5d42759331
2025-09-10 11:21:34.135 | INFO     | f85082f92d904e7282e80ddae8bb68d8 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 7823.265ms
2025-09-10 11:22:19.407 | INFO     | fece041bf8af4e498f74123183b1e61b | 成功认证Java用户: admin
2025-09-10 11:22:19.825 | INFO     | fece041bf8af4e498f74123183b1e61b | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:22:19.828 | INFO     | fece041bf8af4e498f74123183b1e61b | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 422.695ms
2025-09-10 11:22:19.831 | INFO     | 51e0eb670b934a5381e480480c00641a | 成功认证Java用户: admin
2025-09-10 11:22:20.247 | INFO     | 51e0eb670b934a5381e480480c00641a | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:22:20.249 | INFO     | 51e0eb670b934a5381e480480c00641a | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 419.937ms
2025-09-10 11:22:20.251 | INFO     | e94870d7b8d648a48e19508b85ed9740 | 成功认证Java用户: admin
2025-09-10 11:22:20.695 | INFO     | e94870d7b8d648a48e19508b85ed9740 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:22:20.698 | INFO     | e94870d7b8d648a48e19508b85ed9740 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 447.483ms
2025-09-10 11:22:51.783 | INFO     | 48d739b7a4a14271a7116b6d94bb8e04 | 成功认证Java用户: admin
2025-09-10 11:22:52.167 | INFO     | 48d739b7a4a14271a7116b6d94bb8e04 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:22:52.169 | INFO     | 48d739b7a4a14271a7116b6d94bb8e04 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 388.103ms
2025-09-10 11:24:25.720 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:24:33.224 | INFO     | 6b3a4c1ef659432b87e88ba3b0e57b93 | 成功认证Java用户: admin
2025-09-10 11:24:33.659 | INFO     | 6b3a4c1ef659432b87e88ba3b0e57b93 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:24:33.661 | INFO     | 6b3a4c1ef659432b87e88ba3b0e57b93 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 445.008ms
2025-09-10 11:24:52.957 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 成功认证Java用户: admin
2025-09-10 11:24:52.961 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:24:52.961 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 初始化LangGraph智能体服务...
2025-09-10 11:24:52.962 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 加载了 3 个工具
2025-09-10 11:24:52.964 | INFO     | 3bb641b422e24f748d6203717aa8d026 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:24:52.964 | INFO     | 3bb641b422e24f748d6203717aa8d026 | LangGraph智能体服务初始化完成
2025-09-10 11:24:59.175 | INFO     | 3bb641b422e24f748d6203717aa8d026 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:24:59.406 | INFO     | 0b3bdc8445ae4d758ab63aeb88ab08ed | 成功认证Java用户: admin
2025-09-10 11:24:59.812 | INFO     | 0b3bdc8445ae4d758ab63aeb88ab08ed | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:24:59.814 | INFO     | 0b3bdc8445ae4d758ab63aeb88ab08ed | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 409.597ms
2025-09-10 11:25:02.272 | INFO     | 3bb641b422e24f748d6203717aa8d026 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。因为这是一个基本的加法运算，没有涉及三角函数或对数等高
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:25:02.273 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-f98f975c0cb4465fb2e4e4a2b4cf9712', 'type': 'tool_call'}
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 提取的工具信息: name=calculator, args={'expression': '2 + 3'}, id=chatcmpl-tool-f98f975c0cb4465fb2e4e4a2b4cf9712
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T11:25:02.274984', 'end_time': '2025-09-10T11:25:02.274984', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-f98f975c0cb4465fb2e4e4a2b4cf9712'}}
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2加3，我应该用哪个工具呢？看看提供的工具，有一个calculator函数，可以处理基本的数学运算，包括加法。所以应该调用这个工具。参数需要表达式字符串，这里就是"2
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:25:02.274 | INFO     | 3bb641b422e24f748d6203717aa8d026 | LangGraph智能体执行完成: c83abcb5-edb0-4064-89c6-a1b06972d20b
2025-09-10 11:25:02.275 | INFO     | 3bb641b422e24f748d6203717aa8d026 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 9320.470ms
2025-09-10 11:26:51.945 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:27:07.040 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 成功认证Java用户: admin
2025-09-10 11:27:07.044 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 用户 1 发起统一聊天: 你好，请计算2+3... (模式: agent)
2025-09-10 11:27:07.044 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 初始化LangGraph智能体服务...
2025-09-10 11:27:07.044 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 加载了 3 个工具
2025-09-10 11:27:07.047 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | LangGraph状态图构建完成，包含 3 个工具
2025-09-10 11:27:07.047 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | LangGraph智能体服务初始化完成
2025-09-10 11:27:12.115 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:27:16.001 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | HTTP Request: POST http://192.168.2.188:8888/v1/chat/completions "HTTP/1.1 200 OK"
2025-09-10 11:27:16.003 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 开始提取工具调用信息，消息数量: 4
2025-09-10 11:27:16.004 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 消息 0: 类型=HumanMessage, 内容=你好，请计算2+3
2025-09-10 11:27:16.004 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 消息 1: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3。首先，我需要确定使用哪个工具。提供的工具有calculator和advanced_calculator。这个计算是基本的加法，所以应该用calculator。
2025-09-10 11:27:16.004 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 发现AI消息包含工具调用: 1 个
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 工具调用 0: 类型=<class 'dict'>, 内容={'name': 'calculator', 'args': {'expression': '2 + 3'}, 'id': 'chatcmpl-tool-534bc843c9c34987b70ec7f86deeb9b1', 'type': 'tool_call'}
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 提取的工具信息: name=calculator, args={'expression': '2 + 3'}, id=chatcmpl-tool-534bc843c9c34987b70ec7f86deeb9b1
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 成功添加工具调用信息: {'tool_name': 'calculator', 'tool_display_name': 'calculator', 'inputs': {'expression': '2 + 3'}, 'status': 'success', 'start_time': '2025-09-10T11:27:16.005689', 'end_time': '2025-09-10T11:27:16.005689', 'execution_time': 0.1, 'outputs': {'result': '计算结果: 5'}, 'error_message': None, 'metadata': {'tool_call_id': 'chatcmpl-tool-534bc843c9c34987b70ec7f86deeb9b1'}}
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 消息 2: 类型=ToolMessage, 内容=计算结果: 5
2025-09-10 11:27:16.005 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 消息 3: 类型=AIMessage, 内容=<think>
好的，用户让我计算2+3，我之前调用了计算器工具，得到的结果是5。现在需要把这个结果用自然语言回复给用户。因为问题很简单，直接告诉用户答案就可以了。不需要额外的解释，保持回答简洁明了。
2025-09-10 11:27:16.007 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 工具调用信息提取完成，共提取 1 个工具调用
2025-09-10 11:27:16.007 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | LangGraph智能体执行完成: d645944b-2d13-4f8f-9ff7-b279fd0f5720
2025-09-10 11:27:16.009 | INFO     | e11d02aa5aab4198ac7d8b49fd99b641 | 127.0.0.1       | POST     | 200    | /api/iot/v1/chat/chat | 8976.007ms
2025-09-10 11:30:06.262 | INFO     | 4a030242d7624d19a65e94c2c1c9b2f3 | 成功认证Java用户: admin
2025-09-10 11:30:06.970 | INFO     | 4a030242d7624d19a65e94c2c1c9b2f3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:30:06.973 | INFO     | 4a030242d7624d19a65e94c2c1c9b2f3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 714.962ms
2025-09-10 11:31:07.120 | INFO     | 9745333e2466430180d4e61409ad34cb | 成功认证Java用户: admin
2025-09-10 11:31:07.122 | INFO     | 9745333e2466430180d4e61409ad34cb | 已清空用户 1 的会话 chat_session_1757475006235_x1z2qrujst 历史记录
2025-09-10 11:31:07.122 | INFO     | 9745333e2466430180d4e61409ad34cb | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757475006235_x1z2qrujst | 4.360ms
2025-09-10 11:31:20.586 | INFO     | 3919189d1ab14e5ba9e8e398ac308c49 | 成功认证Java用户: admin
2025-09-10 11:31:21.121 | INFO     | 3919189d1ab14e5ba9e8e398ac308c49 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:31:21.123 | INFO     | 3919189d1ab14e5ba9e8e398ac308c49 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 537.522ms
2025-09-10 11:31:49.095 | INFO     | 795368a94ca941f09dc54a26871c5f52 | 成功认证Java用户: admin
2025-09-10 11:31:49.491 | INFO     | 795368a94ca941f09dc54a26871c5f52 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:31:49.493 | INFO     | 795368a94ca941f09dc54a26871c5f52 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 399.196ms
2025-09-10 11:32:03.838 | INFO     | 6d44e3d878ad456c95f0ebfe190457df | 成功认证Java用户: admin
2025-09-10 11:32:04.226 | INFO     | 6d44e3d878ad456c95f0ebfe190457df | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:32:04.227 | INFO     | 6d44e3d878ad456c95f0ebfe190457df | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 390.748ms
2025-09-10 11:32:36.979 | INFO     | d092dc77fd3d443b8f4adf2088b5df7f | 成功认证Java用户: admin
2025-09-10 11:32:36.981 | INFO     | d092dc77fd3d443b8f4adf2088b5df7f | 已清空用户 1 的会话 chat_session_1757475123811_x1gqk61nh0d 历史记录
2025-09-10 11:32:36.981 | INFO     | d092dc77fd3d443b8f4adf2088b5df7f | 192.168.66.13   | DELETE   | 200    | /api/iot/v1/ai/chat/history/chat_session_1757475123811_x1gqk61nh0d | 4.217ms
2025-09-10 11:37:41.840 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-09-10 11:38:21.577 | INFO     | 1eecdce4586d44c0b7788439f2385ebc | 127.0.0.1       | POST     | 401    | /api/iot/v1/chat/chat/stream | 28.946ms
2025-09-10 11:39:03.470 | INFO     | 20cfce1ace9c42878472de3f0d794a71 | 成功认证Java用户: admin
2025-09-10 11:39:03.473 | INFO     | 20cfce1ace9c42878472de3f0d794a71 | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat/stream | 17.221ms
2025-09-10 11:39:14.449 | INFO     | 50610ab0cbb1444ea689d570b2d0f6ce | 成功认证Java用户: admin
2025-09-10 11:39:14.451 | INFO     | 50610ab0cbb1444ea689d570b2d0f6ce | 127.0.0.1       | POST     | 422    | /api/iot/v1/chat/chat/stream | 3.289ms
2025-09-10 11:40:07.410 | INFO     | 763bfa5c09454c62b7fb5fe999327c7f | 成功认证Java用户: admin
2025-09-10 11:40:07.849 | INFO     | 763bfa5c09454c62b7fb5fe999327c7f | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:40:07.851 | INFO     | 763bfa5c09454c62b7fb5fe999327c7f | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 443.323ms
2025-09-10 11:41:03.405 | INFO     | b8801c58e2b1476292dc954be8c96cb5 | 成功认证Java用户: admin
2025-09-10 11:41:03.781 | INFO     | b8801c58e2b1476292dc954be8c96cb5 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:41:03.783 | INFO     | b8801c58e2b1476292dc954be8c96cb5 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 379.867ms
2025-09-10 11:41:21.404 | INFO     | 35471adc56ce40b18ce7c930bc546ba3 | 成功认证Java用户: admin
2025-09-10 11:41:21.799 | INFO     | 35471adc56ce40b18ce7c930bc546ba3 | HTTP Request: GET http://192.168.2.188:8888/v1/models "HTTP/1.1 200 OK"
2025-09-10 11:41:21.800 | INFO     | 35471adc56ce40b18ce7c930bc546ba3 | 192.168.66.13   | GET      | 200    | /api/iot/v1/ai/health | 398.753ms
