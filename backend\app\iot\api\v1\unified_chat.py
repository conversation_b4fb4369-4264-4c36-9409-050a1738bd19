#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一聊天API

集成AI聊天和智能体功能，提供统一的聊天接口
"""
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from loguru import logger
from pydantic import BaseModel, Field

from backend.app.iot.service.ai_chat_service import AIChatService
from backend.app.iot.service.langgraph_agent_service import LangGraphAgentService
from backend.app.iot.schema.agent import AgentConfig
from backend.common.response.response_schema import ResponseSchemaModel, response_base
from fastapi import Request
from backend.database.db import CurrentSession


router = APIRouter()


class UnifiedChatRequest(BaseModel):
    """统一聊天请求"""
    message: str = Field(..., description="用户消息")
    session_id: Optional[str] = Field(None, description="会话ID")
    mode: str = Field("auto", description="聊天模式: auto(自动), ai_only(仅AI), agent(智能体)")
    model: Optional[str] = Field("Qwen3-32B-AWQ", description="使用的AI模型")
    temperature: Optional[float] = Field(0.7, description="生成温度")
    max_tokens: Optional[int] = Field(None, description="最大token数量")
    stream: bool = Field(False, description="是否使用流式响应")
    agent_config: Optional[AgentConfig] = Field(None, description="智能体配置")
    context: Optional[Dict[str, Any]] = Field(None, description="上下文信息")


class UnifiedChatResponse(BaseModel):
    """统一聊天响应"""
    session_id: str = Field(..., description="会话ID")
    message_id: str = Field(..., description="消息ID")
    content: str = Field(..., description="回复内容")
    mode_used: str = Field(..., description="实际使用的模式")
    model_used: str = Field(..., description="使用的模型")
    tool_calls: List[Dict[str, Any]] = Field(default_factory=list, description="工具调用信息")
    execution_summary: Optional[str] = Field(None, description="执行摘要")
    response_time: float = Field(..., description="响应时间")
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token使用情况")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


@router.post("/chat", summary="统一聊天接口", response_model=ResponseSchemaModel[UnifiedChatResponse])
async def unified_chat(
    chat_request: UnifiedChatRequest,
    request: Request,
    db: CurrentSession
):
    """
    统一聊天接口
    
    根据用户消息内容和配置，自动选择使用AI聊天或智能体模式
    """
    try:
        start_time = datetime.now()
        
        # 获取当前用户
        current_user = request.user

        # 生成会话ID和消息ID
        session_id = chat_request.session_id or str(uuid.uuid4())
        message_id = str(uuid.uuid4())

        logger.info(f"用户 {current_user.id} 发起统一聊天: {chat_request.message[:100]}... (模式: {chat_request.mode})")
        
        # 初始化服务
        ai_chat_service = AIChatService()
        langgraph_agent_service = LangGraphAgentService()

        # 根据模式决定处理方式
        mode_used = chat_request.mode
        tool_calls = []
        execution_summary = None
        token_usage = None

        if chat_request.mode == "ai_only":
            # 仅使用AI聊天
            ai_response = await ai_chat_service.chat(
                message=chat_request.message,
                user_id=current_user.id,
                session_id=session_id,
                model=chat_request.model,
                max_tokens=chat_request.max_tokens,
                temperature=chat_request.temperature
            )
            content = ai_response.reply
            model_used = ai_response.model_used
            token_usage = ai_response.tokens_used

        elif chat_request.mode == "agent":
            # 使用LangGraph智能体模式
            content, tool_calls, execution_summary, model_used = await _execute_langgraph_mode(
                chat_request, session_id, current_user.id, langgraph_agent_service
            )

        else:  # auto模式
            # 自动判断是否需要使用智能体
            needs_agent = await _should_use_agent(chat_request.message, ai_chat_service, current_user.id, session_id)

            if needs_agent:
                # 使用LangGraph智能体
                mode_used = "agent"
                content, tool_calls, execution_summary, model_used = await _execute_langgraph_mode(
                    chat_request, session_id, current_user.id, langgraph_agent_service
                )
            else:
                mode_used = "ai_only"
                ai_response = await ai_chat_service.chat(
                    message=chat_request.message,
                    user_id=current_user.id,
                    session_id=session_id,
                    model=chat_request.model,
                    max_tokens=chat_request.max_tokens,
                    temperature=chat_request.temperature
                )
                content = ai_response.reply
                model_used = ai_response.model_used
                token_usage = ai_response.tokens_used
        
        # 计算响应时间
        response_time = (datetime.now() - start_time).total_seconds()
        
        # 构建响应
        response = UnifiedChatResponse(
            session_id=session_id,
            message_id=message_id,
            content=content,
            mode_used=mode_used,
            model_used=model_used or request.model,
            tool_calls=tool_calls,
            execution_summary=execution_summary,
            response_time=response_time,
            token_usage=token_usage
        )
        
        return response_base.success(data=response)
        
    except Exception as e:
        logger.error(f"统一聊天失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"聊天失败: {str(e)}"
        )


async def _should_use_agent(message: str, ai_chat_service: AIChatService, user_id: int, session_id: str) -> bool:
    """判断是否需要使用智能体模式"""
    try:
        # 使用AI判断是否需要工具
        analysis_prompt = f"""请分析以下用户消息，判断是否需要使用工具来回答：

用户消息：{message}

如果消息涉及以下情况，请回答"需要工具"：
1. 需要搜索知识库或文档
2. 需要查询数据库信息
3. 需要调用外部API
4. 需要执行特定的计算或分析
5. 需要获取实时信息

如果是一般性对话、解释概念、创意写作等，请回答"不需要工具"。

请只回答"需要工具"或"不需要工具"。"""
        
        analysis_response = await ai_chat_service.chat(
            message=analysis_prompt,
            user_id=user_id,
            session_id=f"{session_id}_analysis",
            model="Qwen3-32B-AWQ",
            temperature=0.1  # 使用低温度确保判断的一致性
        )
        
        return "需要工具" in analysis_response.reply
        
    except Exception as e:
        logger.warning(f"智能体模式判断失败，默认使用AI模式: {e}")
        return False





@router.post("/chat/stream", summary="统一流式聊天接口")
async def unified_chat_stream(
    chat_request: UnifiedChatRequest,
    request: Request,
    db: CurrentSession
):
    """
    统一流式聊天接口
    
    支持实时流式响应
    """
    try:
        # 获取当前用户
        current_user = request.user

        logger.info(f"用户 {current_user.id} 发起统一流式聊天")

        async def generate_stream():
            try:
                import json

                # 生成会话ID
                session_id = chat_request.session_id or str(uuid.uuid4())

                # 发送开始事件
                yield f"data: {json.dumps({'event': 'start', 'session_id': session_id}, ensure_ascii=False)}\n\n"

                # 根据模式处理
                if chat_request.mode == "ai_only" or (chat_request.mode == "auto" and not await _should_use_agent(chat_request.message, AIChatService(), current_user.id, session_id)):
                    # 使用AI流式聊天
                    ai_chat_service = AIChatService()

                    async for stream_response in ai_chat_service.chat_stream(
                        message=chat_request.message,
                        user_id=current_user.id,
                        session_id=session_id,
                        model=chat_request.model,
                        max_tokens=chat_request.max_tokens,
                        temperature=chat_request.temperature
                    ):
                        yield f"data: {json.dumps({'event': 'delta', 'content': stream_response.delta}, ensure_ascii=False)}\n\n"

                else:
                    # 使用智能体模式（暂时不支持流式，返回完整结果）
                    yield f"data: {json.dumps({'event': 'message', 'content': '正在使用智能体处理您的请求...'}, ensure_ascii=False)}\n\n"

                    agent_service = LangGraphAgentService()
                    content, tool_calls, execution_summary, model_used = await _execute_langgraph_mode(
                        chat_request, session_id, current_user.id, agent_service
                    )

                    # 发送工具执行信息
                    if tool_calls:
                        for tool_call in tool_calls:
                            yield f"data: {json.dumps({'event': 'tool_call', 'data': tool_call}, ensure_ascii=False)}\n\n"

                    # 发送最终结果
                    yield f"data: {json.dumps({'event': 'response', 'content': content, 'summary': execution_summary}, ensure_ascii=False)}\n\n"
                
                # 发送结束事件
                yield f"data: {json.dumps({'event': 'end'}, ensure_ascii=False)}\n\n"
                
            except Exception as e:
                logger.error(f"流式聊天异常: {e}")
                yield f"data: {json.dumps({'event': 'error', 'error': str(e)}, ensure_ascii=False)}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
        )

    except Exception as e:
        logger.error(f"统一流式聊天失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"聊天失败: {str(e)}"
        )


async def _execute_langgraph_mode(
    request: UnifiedChatRequest,
    session_id: str,
    user_id: int,
    langgraph_agent_service: LangGraphAgentService
):
    """
    执行LangGraph智能体模式

    Args:
        request: 聊天请求
        session_id: 会话ID
        user_id: 用户ID
        langgraph_agent_service: LangGraph智能体服务

    Returns:
        Tuple[str, List[Dict], str, str]: 内容、工具调用、执行摘要、使用的模型
    """
    try:
        # 执行LangGraph智能体对话
        result = await langgraph_agent_service.execute_conversation(
            message=request.message,
            session_id=session_id,
            user_id=user_id,
            config=request.agent_config
        )

        if result.success:
            content = result.response
            tool_calls = result.tool_calls or []
            execution_summary = f"LangGraph智能体执行成功，耗时 {result.total_execution_time:.2f}s"
            model_used = request.model or "LangGraph-Agent"

            # 格式化工具调用信息 - 匹配前端期望的格式
            formatted_tool_calls = []
            for tool_call in tool_calls:
                # 处理不同格式的工具调用信息
                if isinstance(tool_call, dict):
                    # 提取基本信息
                    tool_name = tool_call.get("tool_name", tool_call.get("name", "unknown"))
                    arguments = tool_call.get("inputs", tool_call.get("args", {}))
                    result = tool_call.get("outputs", {}).get("result", tool_call.get("result", ""))
                    status = tool_call.get("status", "success")
                    execution_time = tool_call.get("execution_time", 0.1)
                    error_message = tool_call.get("error_message")

                    # 构建前端期望的格式
                    formatted_tool_call = {
                        "tool_name": tool_name,
                        "success": status == "success",
                        "execution_time": execution_time,
                        "arguments": arguments,
                        "outputs": {"result": result} if result else {},
                        "error_message": error_message
                    }
                    formatted_tool_calls.append(formatted_tool_call)
                else:
                    # 如果是对象格式，尝试访问属性
                    tool_name = getattr(tool_call, "tool_name", getattr(tool_call, "name", "unknown"))
                    arguments = getattr(tool_call, "inputs", getattr(tool_call, "args", {}))
                    result = getattr(tool_call, "outputs", {}).get("result", getattr(tool_call, "result", ""))
                    status = getattr(tool_call, "status", "success")
                    execution_time = getattr(tool_call, "execution_time", 0.1)
                    error_message = getattr(tool_call, "error_message", None)

                    formatted_tool_call = {
                        "tool_name": tool_name,
                        "success": status == "success",
                        "execution_time": execution_time,
                        "arguments": arguments,
                        "outputs": {"result": result} if result else {},
                        "error_message": error_message
                    }
                    formatted_tool_calls.append(formatted_tool_call)

            return content, formatted_tool_calls, execution_summary, model_used
        else:
            # 执行失败，返回错误信息
            error_content = f"LangGraph智能体执行失败: {result.error_message}"
            return error_content, [], "执行失败", request.model or "LangGraph-Agent"

    except Exception as e:
        logger.error(f"LangGraph智能体模式执行异常: {e}")
        error_content = f"LangGraph智能体执行异常: {str(e)}"
        return error_content, [], "执行异常", request.model or "LangGraph-Agent"
