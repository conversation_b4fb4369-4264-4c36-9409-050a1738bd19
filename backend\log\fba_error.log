2025-09-10 10:28:59.494 | ERROR    | 55abc599bcf5426d9cff46bea73af500 | Java token认证失败: 401: Token 格式错误
2025-09-10 10:39:47.299 | ERROR    | 54984ac4737b4e5e974b3233587ed7ed | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:39:49.347 | ERROR    | ec9c50299ca6469b96fc3fb56391cae9 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:39:51.389 | ERROR    | c5932a72aeca4effafab42c84d9c4f88 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:46:26.889 | ERROR    | 3542dc0775f0452f8450d38384460119 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:46:28.940 | ERROR    | cca27205719846f48c5866fdeda3fe24 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:46:30.998 | ERROR    | c88ab5ce1cf04300b201f9d184f4fb25 | 统一聊天失败: 'HTTPAuthorizationCredentials' object has no attribute 'id'
2025-09-10 10:47:26.006 | ERROR    | b24898462efa4663a51e2bba3ffd369f | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:47:26.007 | ERROR    | b24898462efa4663a51e2bba3ffd369f | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:47:26.016 | ERROR    | b24898462efa4663a51e2bba3ffd369f | 统一聊天失败: object ResponseModel can't be used in 'await' expression
2025-09-10 10:47:28.073 | ERROR    | 4d614edf174f4e6a8d2d3d16d131f0df | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41610 tokens (650 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:47:28.074 | ERROR    | 4d614edf174f4e6a8d2d3d16d131f0df | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:47:28.074 | ERROR    | 4d614edf174f4e6a8d2d3d16d131f0df | 统一聊天失败: object ResponseModel can't be used in 'await' expression
2025-09-10 10:47:30.501 | ERROR    | 1661401f64cd43049f4e169322fdf4aa | AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:47:30.889 | ERROR    | 1661401f64cd43049f4e169322fdf4aa | AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:47:30.889 | ERROR    | 1661401f64cd43049f4e169322fdf4aa | 统一聊天失败: AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:50:14.337 | ERROR    | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:50:14.337 | ERROR    | 7416a49d20e4461c9bd7dc2b2fbba0c4 | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:50:14.337 | ERROR    | 7416a49d20e4461c9bd7dc2b2fbba0c4 | 统一聊天失败: object ResponseModel can't be used in 'await' expression
2025-09-10 10:50:16.428 | ERROR    | 8627fd1a179d4bb6a3499e8678e7dc76 | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41610 tokens (650 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:50:16.429 | ERROR    | 8627fd1a179d4bb6a3499e8678e7dc76 | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:50:16.429 | ERROR    | 8627fd1a179d4bb6a3499e8678e7dc76 | 统一聊天失败: object ResponseModel can't be used in 'await' expression
2025-09-10 10:50:18.859 | ERROR    | 523576b059764124bd01b2cec9b3745e | AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:50:19.242 | ERROR    | 523576b059764124bd01b2cec9b3745e | AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:50:19.242 | ERROR    | 523576b059764124bd01b2cec9b3745e | 统一聊天失败: AI聊天失败: AI服务调用失败: 404 {"object":"error","message":"The model `Qwen2.5-72B` does not exist.","type":"NotFoundError","param":null,"code":404}
2025-09-10 10:53:28.324 | ERROR    | a10e535d08394683aaf6d9f07f0e5b01 | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:53:28.326 | ERROR    | a10e535d08394683aaf6d9f07f0e5b01 | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:54:29.679 | ERROR    | 04509ae667754c68b310906b51c3f9fb | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 10:54:29.681 | ERROR    | 04509ae667754c68b310906b51c3f9fb | LangGraph智能体模式执行异常: 'AgentExecutionResult' object has no attribute 'execution_time'
2025-09-10 10:56:45.305 | ERROR    | ad6a6e2f3902488fb6c97448ba593ac6 | 工具聊天失败: Error code: 400 - {'object': 'error', 'message': "This model's maximum context length is 40960 tokens. However, you requested 41608 tokens (648 in the messages, 40960 in the completion). Please reduce the length of the messages or completion.", 'type': 'BadRequestError', 'param': None, 'code': 400}
2025-09-10 11:15:03.129 | ERROR    | 492673091b954ec9b21d448328296317 | LangGraph智能体执行失败: 'function'
2025-09-10 11:21:34.134 | ERROR    | f85082f92d904e7282e80ddae8bb68d8 | LangGraph智能体模式执行异常: 'ToolCallInfo' object has no attribute 'get'
